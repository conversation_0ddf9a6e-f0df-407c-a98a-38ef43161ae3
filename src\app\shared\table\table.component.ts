import { CommonModule, DatePipe } from '@angular/common';
import { Component, Directive, Injectable, Input, OnInit, Pipe, PipeTransform, signal, TemplateRef, WritableSignal, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NgPipesModule } from 'ngx-pipes';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { TableModule } from 'primeng/table';

import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';

export type action = {
  label: string;
  icon: string;
  class?:
    | 'p-button-secondary'
    | 'p-button-success'
    | 'p-button-info'
    | 'p-button-warning'
    | 'p-button-help'
    | 'p-button-danger';
  command: (data: any) => void;
  prodection?: (data: any) => boolean;
  hide?: (data: any) => boolean;
};
export type column = {
  header: string;
  key: string;
  sort: boolean;
  type:
    | 'default'
    | 'config'
    | 'check'
    | 'link'
    | 'chip'
    | 'progress'
    | 'flow'
    | 'timeStamp';
  parent?: string;
  colspan?: string;
  filter?: boolean;
  options?: string[];
  action?: () => void;
};
@Pipe({
  name: 'templateRef',
  standalone: true,
})
export class TemplateRefPipe implements PipeTransform {
  private registry = inject(TemplateRegistry, { host: true });

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  transform(name: string): TemplateRef<any> | null {
    return this.registry.templates[name];
  }
}
@Directive({
  selector: '[templateRef]',
  standalone: true,
})
export class TemplateRefDirective {
  private registry = inject(TemplateRegistry, { host: true });
  private template = inject<TemplateRef<any>>(TemplateRef);

  @Input('templateRef')
  templateRef?: string;
  private name?: string;

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  ngOnInit(): void {
    this.name = this.templateRef;
    if (this.name) this.registry.templates[this.name] = this.template;
  }

  ngOnDestroy() {
    if (this.name) delete this.registry.templates[this.name];
  }
}
@Injectable()
export class TemplateRegistry {
  templates: { [name: string]: TemplateRef<any> } = Object.create(null);
}

@Component({
  selector: 'app-table',
  standalone: true,
  imports: [
    TableModule,
    CommonModule,
    ButtonModule,
    FormsModule,
    NgPipesModule,
    InputTextModule,
    TooltipModule,
    OverlayPanelModule,
    TemplateRefPipe,
    TemplateRefDirective,
    ToastModule,
    DatePipe,
    TranslateModule,
  ],
  viewProviders: [TemplateRegistry],
  styles: [
    `
      .p-overlaypanel.p-overlaypanel-content {
        padding: 0.5rem !important;
      }
    `,
    `
      .p-sortable-column-icon {
        color: #545ae8;
      }
    `,
  ],
  templateUrl: './table.component.html',
})
export class TableComponent implements OnInit {
  _cols: column[] = [];
  _data: any[] = [];
  _actions: action[] = [];
  _filter = false;
  _multiSelect = false;
  selectedColumns: column[] = [];
  _multiSelectSignal = signal<any>([]);

  @Input() set cols(cols: column[]) {
    const v = [...cols];
    this._cols = v;
    this.selectedColumns = v;
  }
  get cols() {
    return this._cols;
  }
  @Input() set multiSelect(multiSelect: boolean) {
    this._multiSelect = multiSelect;
  }
  get multiSelect() {
    return this._multiSelect;
  }
  @Input() set multiSelectSignal(multiSelectSignal: WritableSignal<any>) {
    this._multiSelectSignal = multiSelectSignal;
  }
  @Input() set data(data: any[]) {
    if (data) this._data = [...data];
  }
  get data() {
    return this._data;
  }
  @Input() set actions(actions: action[]) {
    this._actions = [...actions];
  }
  get actions() {
    return this._actions;
  }
  @Input() set filter(filter: boolean) {
    this._filter = filter;
  }
  get filter() {
    return this._filter;
  }
  ngOnInit(): void {}
  exac(item: action, data: any) {
    item.command(data);
  }

  //   <app-table
  //   [cols]="cols"
  //   [data]="data"
  //   [actions]="actions"
  //   [filter]="true"
  //   [selectCols]="true"
  //   [all_cols]="all_cols"
  // ></app-table>
}
