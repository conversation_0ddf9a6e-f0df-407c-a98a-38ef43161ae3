import { CommonModule } from '@angular/common';
import { Component, computed, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { Router } from '@angular/router';
import { LatLng } from 'leaflet';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { StepperModule } from 'primeng/stepper';
import { TagModule } from 'primeng/tag';
import { NominatimService } from '../../services/nominatim.service';
import { UserService } from '../../services/user.service';
import { DriverStatus } from '../../core/types/tripoos.types';
import {
    CollaborativeTripService,
    CreateCollaborativeTripOfferDto,
} from '../../services/collaborative-trip.service';
import { injectMany } from '../../shared/helpers/injectMany';
import { decode } from '../../shared/helpers/helpers';
import { ReverseGeocodingResult } from '../../shared/types/nominatim.types';
import { NearPickupLocationStepComponent } from './steps/near-pickup-location-step.component';
import { OrderLocationStepComponent } from './steps/order-location-step.component';
import { TimeStepComponent } from './steps/time-step.component';

export enum ScheduledTripStep {
    ORDER_LOCATION = 0,
    TIME = 1,
    NEAR_PICKUP_LOCATION = 2,
    CONFIRM_SCHEDULED_TRIP = 3,
}

export interface ScheduledTripData {
    pickupLocation?: LatLng;
    dropoffLocation?: LatLng;
    pickupAddress?: string;
    dropoffAddress?: string;
    pickupAddressDetails?: ReverseGeocodingResult;
    dropoffAddressDetails?: ReverseGeocodingResult;
    scheduledDateTime?: Date;
    selectedDaysOfWeek?: number[]; // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    nearPickupLocation?: LatLng;
    nearPickupAddress?: string;
    nearPickupAddressDetails?: ReverseGeocodingResult;
    nearPickupLocations?: Array<{
        location: LatLng;
        address: string;
    }>;
    nearPickupStopPointIds?: string[];
    waypoints?: Array<{
        location: LatLng;
        address?: string;
    }>;
    availableSeats?: number;
    pricePerSeat?: number;
    routePolyline?: string;
    routeLegs?: Array<{
        distance: number;
        duration: number;
        polyline: string;
    }>;
}

@Component({
    selector: 'app-scheduled-trip-steps',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CardModule,
        StepperModule,
        DialogModule,
        TagModule,
        OrderLocationStepComponent,
        TimeStepComponent,
        NearPickupLocationStepComponent,
    ],
    templateUrl: './scheduled-trip-steps.component.html',
    styles: `
        :host {
            display: block;
            flex-grow: 1;
            max-height: 97dvh;
        }
    `,
    providers: [MessageService],
})
export class ScheduledTripStepsComponent implements OnInit, OnDestroy {
    services = injectMany({
        MessageService,
        Router,
        NominatimService,
        CollaborativeTripService,
        UserService,
    });

    // Make enum available in template
    ScheduledTripStep = ScheduledTripStep;

    // State management
    currentStep = signal<ScheduledTripStep>(ScheduledTripStep.ORDER_LOCATION);
    isLoading = signal<boolean>(false);

    // Trip data
    tripData = signal<ScheduledTripData>({});

    // Computed properties
    canProceedToTime = computed(() => {
        const data = this.tripData();
        return (
            data.pickupLocation !== undefined &&
            data.dropoffLocation !== undefined
        );
    });

    canProceedToNearPickup = computed(() => {
        const data = this.tripData();
        return this.canProceedToTime() && data.scheduledDateTime !== undefined;
    });

    canConfirmTrip = computed(() => {
        const data = this.tripData();
        return (
            this.canProceedToNearPickup() &&
            data.nearPickupStopPointIds !== undefined &&
            data.nearPickupStopPointIds.length > 0
        );
    });

    ngOnInit(): void {
        // Initialize component
    }

    ngOnDestroy(): void {
        // Cleanup if needed
    }

    /**
     * Handle order location step completion
     */
    onOrderLocationCompleted(data: {
        pickup: LatLng;
        dropoff: LatLng;
        pickupAddress?: string;
        dropoffAddress?: string;
        waypoints?: Array<{
            location: LatLng;
            address?: string;
        }>;
        routePolyline?: string;
        routeLegs?: Array<{
            distance: number;
            duration: number;
            polyline: string;
        }>;
    }): void {
        this.tripData.update((current) => ({
            ...current,
            pickupLocation: data.pickup,
            dropoffLocation: data.dropoff,
            pickupAddress: data.pickupAddress,
            dropoffAddress: data.dropoffAddress,
            waypoints: data.waypoints,
            routePolyline: data.routePolyline,
            routeLegs: data.routeLegs,
        }));
        this.proceedToNextStep();
    }

    /**
     * Handle time step completion
     */
    onTimeCompleted(data: {
        dateTime: Date;
        daysOfWeek: number[];
        availableSeats: number;
        pricePerSeat: number;
    }): void {
        this.tripData.update((current) => ({
            ...current,
            scheduledDateTime: data.dateTime,
            selectedDaysOfWeek: data.daysOfWeek,
            availableSeats: data.availableSeats,
            pricePerSeat: data.pricePerSeat,
        }));
        this.proceedToNextStep();
    }

    /**
     * Handle near pickup location step completion
     */
    onNearPickupLocationCompleted(data: { stopPointIds: string[] }): void {
        this.tripData.update((current) => ({
            ...current,
            nearPickupStopPointIds: data.stopPointIds,
        }));
        this.proceedToNextStep();
    }

    /**
     * Proceed to next step
     */
    proceedToNextStep(): void {
        const current = this.currentStep();
        switch (current) {
            case ScheduledTripStep.ORDER_LOCATION:
                if (this.canProceedToTime()) {
                    this.currentStep.set(ScheduledTripStep.TIME);
                }
                break;
            case ScheduledTripStep.TIME:
                if (this.canProceedToNearPickup()) {
                    this.currentStep.set(
                        ScheduledTripStep.NEAR_PICKUP_LOCATION,
                    );
                }
                break;
            case ScheduledTripStep.NEAR_PICKUP_LOCATION:
                if (this.canConfirmTrip()) {
                    this.currentStep.set(
                        ScheduledTripStep.CONFIRM_SCHEDULED_TRIP,
                    );
                }
                break;
        }
    }

    /**
     * Go back to previous step
     */
    goBack(): void {
        const current = this.currentStep();
        switch (current) {
            case ScheduledTripStep.TIME:
                this.currentStep.set(ScheduledTripStep.ORDER_LOCATION);
                break;
            case ScheduledTripStep.NEAR_PICKUP_LOCATION:
                this.currentStep.set(ScheduledTripStep.TIME);
                break;
            case ScheduledTripStep.CONFIRM_SCHEDULED_TRIP:
                this.currentStep.set(ScheduledTripStep.NEAR_PICKUP_LOCATION);
                break;
        }
    }

    /**
     * Format days of week for display
     */
    formatDaysOfWeek(dayValues: number[]): string {
        const dayNames = [
            'Sunday',
            'Monday',
            'Tuesday',
            'Wednesday',
            'Thursday',
            'Friday',
            'Saturday',
        ];

        return dayValues
            .sort()
            .map((dayValue) => dayNames[dayValue])
            .join(', ');
    }

    /**
     * Confirm and create scheduled trip
     */
    async confirmScheduledTrip(): Promise<void> {
        const data = this.tripData();
        console.log('Creating scheduled trip with data:', data);

        if (!this.canConfirmTrip()) {
            console.warn('Cannot confirm trip - missing required data');
            return;
        }

        this.isLoading.set(true);
        await this.createCollaborativeTripOfferInternal(data);
    }

    private async createCollaborativeTripOfferInternal(
        data: ScheduledTripData,
    ): Promise<void> {
        console.log('🚀 Starting collaborative trip offer creation...');
        console.log('📊 Trip data received:', data);

        // Decode route points from polyline if available
        let routePoints: Array<{ lat: number; lng: number }> = [];
        if (data.routePolyline) {
            try {
                const coordinates = decode(data.routePolyline);
                routePoints = coordinates.map((coord) => ({
                    lat: coord[0],
                    lng: coord[1],
                }));
                console.log('🗺️ Decoded route points:', routePoints.length);
            } catch (error) {
                console.warn('⚠️ Failed to decode route polyline:', error);
                // Keep empty array as fallback
            }
        } else {
            console.warn('⚠️ No route polyline available, using empty route points');
        }

        // Create collaborative trip offer DTO
        const createDto: CreateCollaborativeTripOfferDto = {
            scheduledTime: data.scheduledDateTime!.toISOString(),
            startLatitude: data.pickupLocation!.lat,
            startLongitude: data.pickupLocation!.lng,
            endLatitude: data.dropoffLocation!.lat,
            endLongitude: data.dropoffLocation!.lng,
            requiredWaypoints:
                data.waypoints && data.waypoints.length > 0
                    ? data.waypoints.map((waypoint) => ({
                          latitude: waypoint.location.lat,
                          longitude: waypoint.location.lng,
                      }))
                    : undefined, // Don't send empty array, send undefined instead
            availableSeats: Math.floor(data.availableSeats || 3), // Ensure integer
            pricePerSeat: data.pricePerSeat || 10, // Use user input or default to $10
            possiblePickupPoints: data.nearPickupStopPointIds || [],
            driverNotes: `Scheduled trip from ${data.pickupAddress || 'pickup location'} to ${data.dropoffAddress || 'dropoff location'}`,
            routePoints,
        };

        console.log(
            '📝 Creating collaborative trip offer with DTO:',
            createDto,
        );
        console.log('🎯 Selected stop point IDs:', data.nearPickupStopPointIds);

        // Create collaborative trip offer
        this.services.CollaborativeTripService.createCollaborativeTripOffer(
            createDto,
        ).subscribe({
            next: (response) => {
                console.log('✅ API Response received:', response);
                this.isLoading.set(false);
                if (response.data) {
                    console.log(
                        '🎉 Collaborative trip offer created successfully:',
                        response.data,
                    );
                    this.services.MessageService.add({
                        severity: 'success',
                        summary: 'Scheduled Trip Created',
                        detail: 'Your collaborative trip offer has been created successfully!',
                    });

                    // Navigate back or to appropriate page
                    this.services.Router.navigate(['/main']);
                } else {
                    console.error('❌ API returned no data:', response);
                    this.services.MessageService.add({
                        severity: 'error',
                        summary: 'Creation Failed',
                        detail: 'Failed to create collaborative trip offer. Please try again.',
                    });
                }
            },
            error: (error) => {
                this.isLoading.set(false);
                console.error(
                    '❌ Error creating collaborative trip offer:',
                    error,
                );
                console.error('📋 Error details:', {
                    status: error?.status,
                    statusText: error?.statusText,
                    errorBody: error?.error,
                    message: error?.message,
                    url: error?.url,
                });

                // Extract more specific error message
                let errorMessage =
                    'An error occurred while creating the collaborative trip offer.';
                if (error?.error?.message) {
                    errorMessage = error.error.message;
                } else if (error?.message) {
                    errorMessage = error.message;
                } else if (typeof error?.error === 'string') {
                    errorMessage = error.error;
                } else if (error?.status) {
                    errorMessage = `HTTP ${error.status}: ${error.statusText || 'Unknown error'}`;
                }

                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Creation Failed',
                    detail: errorMessage,
                });
            },
        });
    }

    /**
     * Start over - reset everything
     */
    startOver(): void {
        this.tripData.set({});
        this.currentStep.set(ScheduledTripStep.ORDER_LOCATION);
    }
}
