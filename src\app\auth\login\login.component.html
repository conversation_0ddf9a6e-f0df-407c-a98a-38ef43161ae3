<div class="h-screen w-full p-10 flex flex-col items-center max-lg:pt-16 animation-class overflow-hidden" [formGroup]="loginForm">


         <div class="flex flex-col justify-between h-full animation-class">
            <div class="flex flex-col items-center text-center gap-12 mt-[30%]">
                <h1 class="m-0 text-4xl ">{{ 'welcome_back!' | translate }}</h1>

                <div class="w-full flex flex-col gap-4">


        <p-iftalabel>
            <input pInputText type="tel" formControlName="phone_number"
            size="small" [style]="{'width':'100%','height':'60px'}"/>
            <label class="flex items-center !leading-[21px] !top-[3px] !left-2 [&_.p-select-label]:!text-sm" for="phoneNumber">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 19.1429V4.85714C1 4.38376 1.38376 4 1.85714 4H10.1429C10.6162 4 11 4.38376 11 4.85714V19.1429C11 19.6162 10.6162 20 10.1429 20H1.85714C1.38376 20 1 19.6162 1 19.1429Z"
                stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" />
                <path d="M6.57143 16C6.57143 16.3156 6.31559 16.5714 6 16.5714C5.68441 16.5714 5.42857 16.3156 5.42857 16C5.42857 15.6844 5.68441 15.4286 6 15.4286C6.31559 15.4286 6.57143 15.6844 6.57143 16Z"
                stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor"/>
                <path d="M6.0031 15.9964H5.99643V16.0036H6.0031V15.9964Z" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor"/>
            </svg>
            <span class="block pt-[1px]">Phone Number</span>
            </label>
        </p-iftalabel>

        <p-iftalabel class="w-full">
            <p-password autocomplete="new-password"
            styleClass="p-password p-component p-inputwrapper p-input-icon-right"
            [inputStyle]="{'width':'100%','color':'var(--text-color-300)','height':'60px'}"
            [style]="{'width':'100%'}"
            [toggleMask]="true" formControlName="password"
            [feedback]="false" inputId="Password" />
            <label class="flex items-center w-full !top-[3px] left-[10px]"  for="Password">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 8.5C4.89543 8.5 4 9.39543 4 10.5C4 11.6046 4.89543 12.5 6 12.5C7.10457 12.5 8 11.6046 8 10.5C8 9.39543 7.10457 8.5 6 8.5ZM2.5 10.5C2.5 8.567 4.067 7 6 7C7.933 7 9.5 8.567 9.5 10.5C9.5 12.433 7.933 14 6 14C4.067 14 2.5 12.433 2.5 10.5ZM12.75 13.5C13.1642 13.5 13.5 13.8358 13.5 14.25V16H10C9.44772 16 9 16.4477 9 17V20C9 20.5523 9.44771 21 10 21H15C15.5523 21 16 20.5523 16 20V17C16 16.4477 15.5523 16 15 16V14.25C15 13.0074 13.9926 12 12.75 12C11.5074 12 10.5 13.0074 10.5 14.25C10.5 14.6642 10.8358 15 11.25 15C11.6642 15 12 14.6642 12 14.25C12 13.8358 12.3358 13.5 12.75 13.5ZM10.5 17.5V19.5H14.5V17.5H10.5ZM3.30652 17.3473C2.44355 17.9672 1.87354 18.8269 1.58861 19.5067H7.31334C7.72755 19.5067 8.06334 19.8424 8.06334 20.2567C8.06334 20.6709 7.72755 21.0067 7.31334 21.0067H1.38385C0.976029 21.0067 0.578739 20.8398 0.309801 20.5287C0.028542 20.2033 -0.08502 19.7452 0.0692591 19.2863C0.392147 18.3258 1.16392 17.0395 2.43144 16.129C3.72865 15.1973 5.51525 14.6837 7.76082 15.2747C8.1614 15.3801 8.40067 15.7903 8.29526 16.1909C8.18984 16.5914 7.77966 16.8307 7.37908 16.7253C5.58111 16.2521 4.25315 16.6674 3.30652 17.3473Z" fill="currentColor"/>
                </svg>
                Password
            </label>
        </p-iftalabel>

                </div>

            </div>

            <div class="flex flex-col gap-4 w-96 font-bold">
            <span class="text-center font-bold text-sm text-text-color-100">
                {{ 'don’t_have_an_account_?' | translate }}
                <a routerLink="/sign-up"  class="text-text-color-100 font-extrabold cursor-pointer">{{ 'sign_up' | translate}}</a>
            </span>
              <button
              (click)="login()"
              [disabled]="!loginForm.valid"
              [ngClass]="!loginForm.valid ? '!bg-background-color-300 !text-text-color-300 !cursor-auto': 'hover:scale-105'"
              class="text-text-color-200 text-center  w-full p-4 border-none bg-background-color-200 rounded-full duration-500 ease-in-out">{{ 'login' | translate }}</button>
            </div>
     </div>


 </div>

