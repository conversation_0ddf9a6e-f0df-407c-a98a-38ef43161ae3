:host ::ng-deep .p-inputotp-input{
    width: 3rem !important;
    height: 3rem !important;
    font-size: 26px !important;
    color: var(--text-color-100) !important;
    background-color: transparent !important;
}

:host ::ng-deep .p-inputotp-input:focus{
    border-color: var(--border-color-100) !important;
}

:host ::ng-deep .p-inputotp{
    gap: 1rem;
}
.animation-class{
    opacity: 0;
    animation: fadeIn 1s ease-in-out forwards;
  }

  @keyframes fadeIn {
    to {
      opacity: 1;
   }
}
