import { Injectable } from '@angular/core';
import { LatLng, Layer, marker, polyline, divIcon, LatLngTuple } from 'leaflet';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { RouteWaypoint, RouteInfo } from '../shared/types/route.types';
import { ValhallaService } from './valhala.service';
import {
    DirectionsRequest,
    ValhallaLocation,
} from '../shared/types/valhalla.types';
import { decode } from '../shared/helpers/helpers';

@Injectable({
    providedIn: 'root',
})
export class RouteVisualizationService {
    constructor(private valhallaService: ValhallaService) {}
    /**
     * Create map layers for route visualization
     */
    createRouteLayers(routeInfo: RouteInfo): Layer[] {
        const layers: Layer[] = [];

        if (!routeInfo) return layers;

        // Add waypoint markers
        routeInfo.waypoints.forEach((waypoint, index) => {
            const markerLayer = this.createWaypointMarker(waypoint, index);
            layers.push(markerLayer);
        });

        // Add route polylines
        if (routeInfo.legs && routeInfo.legs.length > 0) {
            routeInfo.legs.forEach((leg, index) => {
                if (leg.polyline) {
                    const coordinates = decode(leg.polyline);
                    if (coordinates.length > 0) {
                        const routeLine = this.createRoutePolyline(
                            coordinates,
                            index,
                        );
                        layers.push(routeLine);
                    }
                }
            });
        } else if (routeInfo.polyline) {
            // Fallback to main polyline
            const coordinates = decode(routeInfo.polyline);
            if (coordinates.length > 0) {
                const routeLine = this.createRoutePolyline(coordinates, 0);
                layers.push(routeLine);
            }
        } else {
            // Fallback: create simple line between waypoints
            if (routeInfo.waypoints.length >= 2) {
                const coordinates = routeInfo.waypoints.map(
                    (wp) =>
                        [wp.location.lat, wp.location.lng] as [number, number],
                );
                const simpleLine = polyline(coordinates, {
                    color: '#3B82F6',
                    weight: 4,
                    opacity: 0.7,
                    dashArray: '10, 5', // Dashed line to indicate it's not the actual route
                });
                layers.push(simpleLine);
            }
        }

        return layers;
    }

    /**
     * Create map layers for route visualization using Valhalla service
     */
    createRouteLayersWithValhalla(routeInfo: RouteInfo): Observable<Layer[]> {
        if (!routeInfo || routeInfo.waypoints.length < 2) {
            return of(this.createWaypointLayers(routeInfo));
        }

        // Convert waypoints to Valhalla locations
        const locations: ValhallaLocation[] = routeInfo.waypoints.map((wp) => ({
            lat: wp.location.lat,
            lon: wp.location.lng,
        }));

        const request: DirectionsRequest = {
            locations,
            costing: 'auto',
            directions_options: {
                units: 'kilometers',
                language: 'en',
            },
        };

        return this.valhallaService.getDirections(request).pipe(
            map((response) => {
                const layers: Layer[] = [];

                // Add waypoint markers
                layers.push(...this.createWaypointLayers(routeInfo));

                // Add route polylines from Valhalla response
                if (response.trip && response.trip.legs) {
                    response.trip.legs.forEach((leg, index) => {
                        if (leg.shape) {
                            try {
                                // Use the decode function from helpers
                                const coordinates = decode(leg.shape);
                                if (coordinates.length > 0) {
                                    const routeLine =
                                        this.createRoutePolylineFromCoords(
                                            coordinates,
                                            index,
                                        );
                                    layers.push(routeLine);
                                }
                            } catch (error) {
                                console.warn(
                                    'Failed to decode polyline:',
                                    error,
                                );
                                // Fallback to simple line for this leg
                                const startWaypoint =
                                    routeInfo.waypoints[index];
                                const endWaypoint =
                                    routeInfo.waypoints[index + 1];
                                if (startWaypoint && endWaypoint) {
                                    const simpleLine = polyline(
                                        [
                                            [
                                                startWaypoint.location.lat,
                                                startWaypoint.location.lng,
                                            ],
                                            [
                                                endWaypoint.location.lat,
                                                endWaypoint.location.lng,
                                            ],
                                        ],
                                        {
                                            color: '#3B82F6',
                                            weight: 4,
                                            opacity: 0.7,
                                            dashArray: '10, 5',
                                        },
                                    );
                                    layers.push(simpleLine);
                                }
                            }
                        }
                    });
                }

                return layers;
            }),
            catchError((error) => {
                console.error('Error getting route from Valhalla:', error);
                // Fallback to simple visualization
                return of(this.createRouteLayers(routeInfo));
            }),
        );
    }

    /**
     * Create waypoint markers only
     */
    private createWaypointLayers(routeInfo: RouteInfo): Layer[] {
        const layers: Layer[] = [];

        if (!routeInfo || !routeInfo.waypoints) return layers;

        routeInfo.waypoints.forEach((waypoint, index) => {
            const markerLayer = this.createWaypointMarker(waypoint, index);
            layers.push(markerLayer);
        });

        return layers;
    }

    /**
     * Create a marker for a waypoint
     */
    private createWaypointMarker(
        waypoint: RouteWaypoint,
        index: number,
    ): Layer {
        let markerIcon;

        if (waypoint.isPickup) {
            markerIcon = divIcon({
                html: `
                    <div class="flex h-8 w-8 items-center justify-center rounded-full border-2 border-white bg-green-500 text-sm font-bold text-white shadow-lg">
                        P
                    </div>
                `,
                className: 'custom-marker',
                iconSize: [32, 32],
                iconAnchor: [16, 16],
            });
        } else if (waypoint.isDropoff) {
            markerIcon = divIcon({
                html: `
                    <div class="flex h-8 w-8 items-center justify-center rounded-full border-2 border-white bg-red-500 text-sm font-bold text-white shadow-lg">
                        D
                    </div>
                `,
                className: 'custom-marker',
                iconSize: [32, 32],
                iconAnchor: [16, 16],
            });
        } else {
            // Regular waypoint
            const waypointNumber = index - 1; // Subtract 1 because pickup is index 0
            markerIcon = divIcon({
                html: `
                    <div class="flex h-6 w-6 items-center justify-center rounded-full border-2 border-white bg-blue-500 text-xs font-bold text-white shadow-lg">
                        ${waypointNumber}
                    </div>
                `,
                className: 'custom-marker',
                iconSize: [24, 24],
                iconAnchor: [12, 12],
            });
        }

        const waypointMarker = marker(
            [waypoint.location.lat, waypoint.location.lng],
            {
                icon: markerIcon,
            },
        );

        // Add popup with waypoint information
        if (waypoint.address) {
            waypointMarker.bindPopup(`
                <div class="p-2">
                    <div class="mb-1 text-sm font-semibold">
                        ${waypoint.isPickup ? 'Pickup' : waypoint.isDropoff ? 'Dropoff' : `Waypoint ${index - 1}`}
                    </div>
                    <div class="text-xs text-gray-600">${waypoint.address}</div>
                </div>
            `);
        }

        return waypointMarker;
    }
    private createRoutePolyline(
        coordinates: LatLngTuple[],
        legIndex: number,
    ): Layer {
        const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
        const color = colors[legIndex % colors.length];

        return polyline(coordinates, {
            color: color,
            weight: 4,
            opacity: 0.8,
            smoothFactor: 1,
        });
    }

    /**
     * Create a polyline from decoded coordinates
     */
    private createRoutePolylineFromCoords(
        coordinates: LatLngTuple[],
        legIndex: number,
    ): Layer {
        const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
        const color = colors[legIndex % colors.length];

        return polyline(coordinates, {
            color: color,
            weight: 4,
            opacity: 0.8,
            smoothFactor: 1,
        });
    }

    /**
     * Format distance for display
     */
    formatDistance(distanceKm: number): string {
        if (distanceKm < 1) {
            return `${Math.round(distanceKm * 1000)} m`;
        }
        return `${distanceKm.toFixed(1)} km`;
    }

    /**
     * Format duration for display
     */
    formatDuration(durationMinutes: number): string {
        if (durationMinutes < 60) {
            return `${Math.round(durationMinutes)} min`;
        }
        const hours = Math.floor(durationMinutes / 60);
        const minutes = Math.round(durationMinutes % 60);
        return `${hours}h ${minutes}m`;
    }
}
