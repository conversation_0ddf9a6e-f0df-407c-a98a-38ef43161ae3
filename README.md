# Tripos Backend

A NestJS-based backend service for the Tripos application.

## 🚀 Quick Start

After cloning the repository, you can get started with these main commands:

```bash
# 1. Initial setup (installs dependencies, starts DB, runs migrations)
npm run setup

# 2. Start the development server
npm run dev

# 3. Open Prisma Studio (Database GUI)
npm run db:studio
```

The server will be running at [http://localhost:3000](http://localhost:3000) by default.

## 📋 Detailed Setup

If you prefer to run the setup steps individually:

### 1. Clone the Repository
```bash
git clone <repository-url>
cd tripos-backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start the Database
Start PostgreSQL using Docker Compose:
```bash
docker-compose up -d
```

### 4. Run Database Migrations
```bash
npm run db:migrate
```

### 5. Start the Development Server
```bash
npm run start:dev
```

## 📚 Available Scripts

### Main Commands
- `npm run setup` — Complete setup (install dependencies, start DB, run migrations)
- `npm run dev` — Start development server with hot-reload
- `npm run db:studio` — Open Prisma Studio (GUI for database management)

### Database Commands
- `npm run db:up` — Start the database
- `npm run db:down` — Stop the database
- `npm run db:reset` — Reset the database (⚠️ deletes all data)
- `npm run db:migrate` — Run database migrations
- `npm run db:generate` — Generate Prisma client

### Utility Commands
- `npm run clean` — Remove build artifacts and dependencies
- `npm run build` — Build the application
- `npm run start:prod` — Start the production server
- `npm run lint` — Run ESLint
- `npm run format` — Format code with Prettier

## 🐳 Docker Commands

```bash
# Start the database
docker-compose up -d

# Stop the database
docker-compose down

# View database logs
docker-compose logs -f
```

## 🔧 Environment Variables

Create a `.env` file in the root directory with the following variables:
```env
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/tripos?schema=public"
# Add other required environment variables
```

## 📝 Notes

- The database runs on PostgreSQL (port 5432)
- Prisma Studio provides a GUI for database management at http://localhost:5555
- Make sure Docker is running before starting the database

