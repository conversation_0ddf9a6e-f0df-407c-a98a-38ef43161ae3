import { CommonModule } from '@angular/common';
import {
    Component,
    computed,
    DestroyRef,
    inject,
    input,
    OnInit,
    output,
    signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LatLng, Layer, latLng } from 'leaflet';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { takeUntil } from 'rxjs';
import { NominatimService } from '../../../services/nominatim.service';
import { RouteVisualizationService } from '../../../services/route-visualization.service';
import { ValhallaService } from '../../../services/valhala.service';
import {
    MarkedPlaceService,
    MarkedPlace,
} from '../../../services/marked-place.service';
import { MapComponent } from '../../../shared/components/map.component';
import { injectMany } from '../../../shared/helpers/injectMany';
import { RouteInfo, RouteWaypoint } from '../../../shared/types/route.types';
import {
    DirectionsRequest,
    ValhallaLocation,
} from '../../../shared/types/valhalla.types';
import { LocationPickerComponent } from '../../order-flow/location-picker.component';
import { ScheduledTripData } from '../scheduled-trip-steps.component';

@Component({
    selector: 'app-order-location-step',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CardModule,
        ProgressSpinnerModule,
        LocationPickerComponent,
        MapComponent,
    ],
    templateUrl: `./order-location-step.component.html`,
    styles: [
        `
            :host {
                display: block;
                height: 100%;
            }

            /* Custom marker styles */
            ::ng-deep .custom-marker {
                background: transparent !important;
                border: none !important;
            }

            ::ng-deep .custom-marker div {
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
        `,
    ],
})
export class OrderLocationStepComponent implements OnInit {
    services = injectMany({
        NominatimService,
        ValhallaService,
        RouteVisualizationService,
        MarkedPlaceService,
    });
    distroyref = inject(DestroyRef);

    // Inputs
    tripData = input<ScheduledTripData>({});

    // Outputs
    completed = output<{
        pickup: LatLng;
        dropoff: LatLng;
        pickupAddress?: string;
        dropoffAddress?: string;
        waypoints?: Array<{
            location: LatLng;
            address?: string;
        }>;
        routePolyline?: string;
        routeLegs?: Array<{
            distance: number;
            duration: number;
            polyline: string;
        }>;
    }>();

    // State
    currentLocationStep = signal<'pickup' | 'dropoff' | 'confirm'>('pickup');
    pickupLocation = signal<LatLng | null>(null);
    dropoffLocation = signal<LatLng | null>(null);
    pickupAddress = signal<string>('');
    dropoffAddress = signal<string>('');
    markedPlaces = signal<MarkedPlace[]>([]);

    // Route management state
    isCalculatingRoute = signal<boolean>(false);
    routeInfo = signal<RouteInfo | null>(null);
    mapLayers = signal<Layer[]>([]);

    // Computed
    canProceedToDropoff = computed(() => this.pickupLocation() !== null);
    canConfirm = computed(
        () => this.pickupLocation() !== null && this.dropoffLocation() !== null,
    );

    waypointCount = computed(() => {
        const route = this.routeInfo();
        return route ? route.waypoints.length : 0;
    });

    // Expose Math for template
    Math = Math;

    ngOnInit(): void {
        this.loadMarkedPlaces();
    }

    /**
     * Get intermediate waypoints (excluding pickup and dropoff)
     */
    IntermediateWaypoints = computed(() => {
        const route = this.routeInfo();
        if (!route || !route.waypoints) return [];

        return route.waypoints.filter((wp) => !wp.isPickup && !wp.isDropoff);
    });

    onPickupSelected(location: LatLng): void {
        this.pickupLocation.set(location);

        // Get address for the location
        this.services.NominatimService.reverseGeocode(
            location.lng,
            location.lat,
        ).subscribe({
            next: (result) => {
                if (result && result.display_name) {
                    this.pickupAddress.set(result.display_name);
                }
                this.currentLocationStep.set('dropoff');
            },
            error: () => {
                this.pickupAddress.set('Selected location');
                this.currentLocationStep.set('dropoff');
            },
        });
    }

    onDropoffSelected(location: LatLng): void {
        this.dropoffLocation.set(location);

        // Get address for the location
        this.services.NominatimService.reverseGeocode(
            location.lng,
            location.lat,
        ).subscribe({
            next: (result) => {
                if (result && result.display_name) {
                    this.dropoffAddress.set(result.display_name);
                }

                this.currentLocationStep.set('confirm');
                this.initializeRoute();
            },
            error: () => {
                this.dropoffAddress.set('Selected location');
                this.currentLocationStep.set('confirm');
                this.initializeRoute();
            },
        });
    }

    confirmLocations(): void {
        const pickup = this.pickupLocation();
        const dropoff = this.dropoffLocation();

        if (pickup && dropoff) {
            // Get intermediate waypoints (excluding pickup and dropoff)
            const intermediateWaypoints = this.IntermediateWaypoints();
            const waypoints = intermediateWaypoints.map((wp) => ({
                location: wp.location,
                address: wp.address,
            }));

            // Get route information if available
            const route = this.routeInfo();
            const routePolyline = route?.polyline;
            const routeLegs = route?.legs;

            this.completed.emit({
                pickup,
                dropoff,
                pickupAddress: this.pickupAddress(),
                dropoffAddress: this.dropoffAddress(),
                waypoints: waypoints.length > 0 ? waypoints : undefined,
                routePolyline,
                routeLegs,
            });
        }
    }

    /**
     * Initialize route with pickup and dropoff points
     */
    private initializeRoute(): void {
        const pickup = this.pickupLocation();
        const dropoff = this.dropoffLocation();
        if (pickup && dropoff) {
            const waypoints: RouteWaypoint[] = [
                {
                    id: 'pickup',
                    location: pickup,
                    address: this.pickupAddress(),
                    isPickup: true,
                },
                {
                    id: 'dropoff',
                    location: dropoff,
                    address: this.dropoffAddress(),
                    isDropoff: true,
                },
            ];

            this.routeInfo.set({ waypoints });
            this.calculateRoute();
        }
    }

    /**
     * Calculate route using Valhalla service
     */
    private calculateRoute(): void {
        const route = this.routeInfo();
        if (!route || route.waypoints.length < 2) return;

        this.isCalculatingRoute.set(true);

        // Convert waypoints to Valhalla locations
        const locations: ValhallaLocation[] = route.waypoints.map((wp) => ({
            lat: wp.location.lat,
            lon: wp.location.lng,
        }));

        const request: DirectionsRequest = {
            locations,
            costing: 'auto',
            directions_options: {
                units: 'kilometers',
                language: 'en',
            },
        };

        this.services.ValhallaService.getDirections(request)
            .pipe(takeUntilDestroyed(this.distroyref))
            .subscribe({
                next: (response) => {
                    this.processRouteResponse(response);
                    this.isCalculatingRoute.set(false);
                },
                error: (error) => {
                    console.error('Error calculating route:', error);
                    this.isCalculatingRoute.set(false);
                    // Still update map with waypoints even if route calculation fails
                    this.updateMapLayers();
                },
            });
    }

    /**
     * Process Valhalla route response
     */
    private processRouteResponse(response: any): void {
        const route = this.routeInfo();
        if (!route) return;

        const trip = response.trip;
        if (trip && trip.legs && trip.legs.length > 0) {
            const totalDistance = trip.summary.length; // in km
            const totalDuration = trip.summary.time / 60; // convert seconds to minutes

            const legs = trip.legs.map((leg: any) => ({
                distance: leg.summary.length,
                duration: leg.summary.time / 60,
                polyline: leg.shape,
            }));

            const updatedRoute: RouteInfo = {
                ...route,
                totalDistance,
                totalDuration,
                polyline: trip.legs[0].shape, // Main polyline
                legs,
            };

            this.routeInfo.set(updatedRoute);
        }

        this.updateMapLayers();
    }

    /**
     * Update map layers with route visualization using Valhalla
     */
    private updateMapLayers(): void {
        const route = this.routeInfo();
        if (!route) return;

        // Use Valhalla service to get accurate route visualization
        this.services.RouteVisualizationService.createRouteLayersWithValhalla(
            route,
        )
            .pipe(takeUntilDestroyed(this.distroyref))
            .subscribe({
                next: (layers: Layer[]) => {
                    this.mapLayers.set(layers);
                },
                error: (error) => {
                    console.error('Error creating route layers:', error);
                    // Fallback to simple visualization
                    const fallbackLayers =
                        this.services.RouteVisualizationService.createRouteLayers(
                            route,
                        );
                    this.mapLayers.set(fallbackLayers);
                },
            });
    }

    /**
     * Handle map click to add waypoints
     */
    onMapClick(event: any): void {
        const latlng = event.latlng as LatLng;
        this.addWaypoint(latlng);
    }

    /**
     * Add a new waypoint to the route
     */
    private addWaypoint(location: LatLng): void {
        const route = this.routeInfo();
        if (!route) return;

        // Insert waypoint before the dropoff
        const newWaypoint: RouteWaypoint = {
            id: `waypoint-${Date.now()}`,
            location,
        };

        const waypoints = [...route.waypoints];
        // Insert before the last waypoint (dropoff)
        waypoints.splice(-1, 0, newWaypoint);

        this.routeInfo.update((current) =>
            current ? { ...current, waypoints } : null,
        );

        // Try to get address for the new waypoint
        this.services.NominatimService.reverseGeocode(
            location.lng,
            location.lat,
        )
            .pipe(takeUntilDestroyed(this.distroyref))
            .subscribe({
                next: (result) => {
                    if (result && result.display_name) {
                        // Update the waypoint with the address
                        this.routeInfo.update((current) => {
                            if (!current) return null;
                            const updatedWaypoints = current.waypoints.map(
                                (wp) =>
                                    wp.id === newWaypoint.id
                                        ? {
                                              ...wp,
                                              address: result.display_name,
                                          }
                                        : wp,
                            );
                            return { ...current, waypoints: updatedWaypoints };
                        });
                    }
                },
                error: () => {
                    // Address lookup failed, but that's okay
                },
            });

        this.calculateRoute();
    }

    /**
     * Remove a waypoint from the route
     */
    removeWaypoint(waypointId: string): void {
        const route = this.routeInfo();
        if (!route) return;

        // Don't allow removing pickup or dropoff
        const waypointToRemove = route.waypoints.find(
            (wp) => wp.id === waypointId,
        );
        if (
            !waypointToRemove ||
            waypointToRemove.isPickup ||
            waypointToRemove.isDropoff
        ) {
            return;
        }

        const updatedWaypoints = route.waypoints.filter(
            (wp) => wp.id !== waypointId,
        );

        this.routeInfo.update((current) =>
            current ? { ...current, waypoints: updatedWaypoints } : null,
        );

        this.calculateRoute();
    }

    /**
     * Recalculate the current route
     */
    recalculateRoute(): void {
        this.calculateRoute();
    }

    /**
     * Go back to dropoff selection
     */
    goBack(): void {
        this.currentLocationStep.set('dropoff');
    }

    /**
     * Load user's marked places
     */
    loadMarkedPlaces(): void {
        this.services.MarkedPlaceService.getAllMarkedPlaces().subscribe({
            next: (response) => {
                if (response.data) {
                    this.markedPlaces.set(response.data);
                }
            },
            error: (error) => {
                console.error('Failed to load marked places:', error);
                // Don't show error toast as this is not critical
            },
        });
    }

    /**
     * Select a marked place as pickup location
     */
    selectMarkedPlace(markedPlace: MarkedPlace): void {
        const location = latLng(markedPlace.latitude, markedPlace.longitude);
        this.pickupLocation.set(location);
        this.pickupAddress.set(markedPlace.name);
        this.currentLocationStep.set('dropoff');
    }

    /**
     * Select a marked place as dropoff location
     */
    selectMarkedPlaceAsDropoff(markedPlace: MarkedPlace): void {
        const location = latLng(markedPlace.latitude, markedPlace.longitude);

        // Check if dropoff is too close to pickup (same location)
        const pickup = this.pickupLocation();
        if (
            pickup &&
            pickup.lat === location.lat &&
            pickup.lng === location.lng
        ) {
            console.warn('Pickup and dropoff locations cannot be the same');
            return;
        }

        this.dropoffLocation.set(location);
        this.dropoffAddress.set(markedPlace.name);
        this.currentLocationStep.set('confirm');
        this.initializeRoute();
    }
}
