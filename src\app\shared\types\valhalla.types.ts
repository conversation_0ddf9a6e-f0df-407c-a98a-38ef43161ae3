export interface LatLng {
    lat: number;
    lng: number;
}

export interface ValhallaLocation {
    lat: number;
    lon: number;
    type?: string;
    heading?: number;
    heading_tolerance?: number;
    street?: string;
    way_id?: number;
    minimum_reachability?: number;
    radius?: number;
}

export interface LocateRequest {
    costing: string;
    locations: ValhallaLocation[];
    costing_options?: Record<string, any>;
    directions_options?: Record<string, any>;
}

export interface LocateResponse {
    input_lat: number;
    input_lon: number;
    nodes: Array<{
        lat: number;
        lon: number;
        edge_id: number;
        percent_along: number;
    }>;
}

export interface HeightRequest {
    range: boolean;
    shape: ValhallaLocation[];
    id: string;
    encoded_polyline?: string;
    resample_distance?: number;
}

export interface HeightResponse {
    height: number[];
    range_height: Array<[number, number]>;
}

export interface IsochroneRequest {
    locations: ValhallaLocation[];
    costing: string;
    contours: Array<{
        time: number;
        distance?: number;
        color?: string;
    }>;
    polygons?: boolean;
    denoise?: number;
    generalize?: number;
    costing_options?: Record<string, any>;
}

export interface IsochroneResponse {
    type: 'FeatureCollection';
    features: Array<{
        type: 'Feature';
        geometry: {
            type: 'Polygon' | 'MultiPolygon';
            coordinates: number[][][];
        };
        properties: {
            contour: number;
            color?: string;
            fill?: string;
            fillOpacity?: number;
            opacity?: number;
        };
    }>;
}

export interface DirectionsRequest {
    locations: ValhallaLocation[];
    costing: string;
    costing_options?: Record<string, any>;
    directions_options?: {
        units: 'miles' | 'kilometers';
        language: string;
    };
}

export interface DirectionsResponse {
    trip: {
        locations: ValhallaLocation[];
        legs: Array<{
            summary: {
                length: number;
                time: number;
                cost: number;
            };
            maneuvers: Array<{
                type: number;
                instruction: string;
                verbal_instruction: string;
                street_names: string[];
                time: number;
                length: number;
                begin_shape_index: number;
                end_shape_index: number;
                verbal_multi_cue: boolean;
                travel_mode: string;
                travel_type: string;
            }>;
            shape: string;
        }>;
        summary: {
            time: number;
            length: number;
            cost: number;
        };
        status_message: string;
        status: number;
        units: 'miles' | 'kilometers';
        language: string;
    };
}

export interface ServerStatus {
    version: string;
    tileset_last_modified: string;
    has_tiles: boolean;
    has_admins: boolean;
    has_timezones: boolean;
}
