import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';

import { MessageService } from 'primeng/api';
import { forkJoin, interval, Subscription } from 'rxjs';
import { CreateOrderDto, Order } from '../../core/types/tripoos.types';
import { NominatimService } from '../../services/nominatim.service';
import { OrderService } from '../../services/order.service';
import { injectMany } from '../../shared/helpers/injectMany';
import { getlocation } from '../../shared/helpers/location';

// Enhanced order interface with pricing and address information
interface EnhancedOrder extends Order {
    pickupAddress?: string;
    dropoffAddress?: string;
    estimatedPrice?: number;
    isLoadingDetails?: boolean;
}

@Component({
    selector: 'app-find-orders',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        DialogModule,
        ProgressSpinnerModule,
        ToastModule,
    ],
    providers: [MessageService],
    templateUrl: './find-orders.component.html',
    styles: `
        .find-orders-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .search-animation {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 80vh;
            text-align: center;
        }

        .search-pulse {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: #000000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .search-ripple {
            position: absolute;
            top: -15px;
            left: -15px;
            right: -15px;
            bottom: -15px;
            border-radius: 50%;
            border: 3px solid #000000;
            animation: ripple 2.5s infinite;
        }

        .search-ripple.delay-1 {
            animation-delay: 0.6s;
            border-width: 2px;
            border-color: #333333;
            top: -25px;
            left: -25px;
            right: -25px;
            bottom: -25px;
        }

        .search-ripple.delay-2 {
            animation-delay: 1.2s;
            border-width: 1px;
            border-color: #666666;
            top: -35px;
            left: -35px;
            right: -35px;
            bottom: -35px;
        }

        @keyframes ripple {
            0% {
                transform: scale(0.7);
                opacity: 1;
            }
            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }

        .search-icon {
            font-size: 3rem;
            color: white;
        }

        .search-text {
            font-size: 1.8rem;
            font-weight: 600;
            color: #000000;
            margin-bottom: 0.5rem;
        }

        .search-subtext {
            font-size: 1.1rem;
            color: #666666;
            font-weight: 400;
            font-style: italic;
        }

        .orders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .order-card {
            background: white;
            border-radius: 16px;
            padding: 0;
            border: 1px solid #e0e0e0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .order-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .order-header {
            background: #000000;
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .order-header-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.5rem;
        }

        @media (max-width: 768px) {
            .order-header-right {
                flex-direction: row;
                align-items: center;
                gap: 0.75rem;
            }

            .price-badge {
                font-size: 0.9rem;
                padding: 0.3rem 0.6rem;
            }
        }

        .order-id {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .order-time {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .order-content {
            padding: 1.5rem;
        }

        .location-row {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .location-icon {
            font-size: 1.2rem;
            margin-right: 1rem;
            width: 24px;
            text-align: center;
        }

        .pickup-icon {
            color: #000000;
        }

        .dropoff-icon {
            color: #333333;
        }

        .location-details {
            flex: 1;
        }

        .location-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .location-address {
            font-size: 0.95rem;
            color: #000;
            font-weight: 500;
        }

        .distance-badge {
            background: #e9ecef;
            color: #000;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .price-badge {
            background: #ffffff;
            color: #000000;
            padding: 0.4rem 0.8rem;
            border-radius: 16px;
            font-size: 1rem;
            font-weight: 700;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .loading-skeleton {
            background: linear-gradient(
                90deg,
                #f0f0f0 25%,
                #e0e0e0 50%,
                #f0f0f0 75%
            );
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
            height: 1.2rem;
            color: transparent;
            font-style: italic;
            font-size: 0.9rem;
        }

        .order-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .accept-btn {
            background: #000 !important;
            border: 1px solid #000 !important;
            color: white !important;
            flex: 1;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .accept-btn:hover {
            background: #333 !important;
            transform: translateY(-2px);
        }

        .decline-btn {
            background: white !important;
            border: 1px solid #000 !important;
            color: #000 !important;
            flex: 1;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .decline-btn:hover {
            background: #f8f9fa !important;
            transform: translateY(-2px);
        }

        .no-orders {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .no-orders-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .loading-shimmer {
            background: linear-gradient(
                90deg,
                #f0f0f0 25%,
                #e0e0e0 50%,
                #f0f0f0 75%
            );
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #000;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            z-index: 1000;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.3s ease-out;
        }

        .refresh-indicator i {
            font-size: 0.9rem;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 0.9;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .orders-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }
        }
    `,
})
export class FindOrdersComponent implements OnInit, OnDestroy {
    private services = injectMany({
        OrderService,
        NominatimService,
        Router,
        MessageService,
    });

    // Signals
    availableOrders = signal<EnhancedOrder[]>([]);
    isLoading = signal<boolean>(false);
    isSearching = signal<boolean>(true);
    selectedOrder = signal<EnhancedOrder | null>(null);
    showOrderDialog = signal<boolean>(false);
    lastUpdated = signal<Date>(new Date());
    userLocation = signal<{ latitude: number; longitude: number } | null>(null);
    isAutoRefreshing = signal<boolean>(false);
    isAutoRefreshPaused = signal<boolean>(false);

    // Subscriptions
    private refreshSubscription?: Subscription;
    private searchSubscription?: Subscription;
    private autoRefreshSubscription?: Subscription;

    ngOnInit(): void {
        this.initializeLocation();
    }

    ngOnDestroy(): void {
        if (this.refreshSubscription) {
            this.refreshSubscription.unsubscribe();
        }
        this.stopAutoRefresh();
    }

    private initializeLocation(): void {
        getlocation().subscribe({
            next: (location) => {
                this.userLocation.set(location);
                this.searchForOrders();
                // Auto-refresh will be managed by searchForOrders() based on results
            },
            error: (error) => {
                console.error('Error getting location:', error);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Location Error',
                    detail: 'Unable to get your location. Please enable location services.',
                });
            },
        });
    }

    private searchForOrders(): void {
        const location = this.userLocation();
        if (!location) return;

        this.isLoading.set(true);

        this.services.OrderService.findNearbyPendingOrders(location).subscribe({
            next: (response) => {
                const newOrders = response.data || [];
                const enhancedOrders: EnhancedOrder[] = newOrders.map(
                    (order) => ({
                        ...order,
                        isLoadingDetails: true,
                    }),
                );

                this.availableOrders.set(enhancedOrders);
                this.isSearching.set(false);
                this.isLoading.set(false);
                this.isAutoRefreshing.set(false);
                this.lastUpdated.set(new Date());

                // Enhance orders with pricing and address information
                if (newOrders.length > 0) {
                    this.enhanceOrdersWithDetails(enhancedOrders);
                }

                // Manage auto-refresh based on orders availability
                if (newOrders.length > 0) {
                    // Stop auto-refresh when orders are found
                    this.stopAutoRefresh();
                    this.isAutoRefreshPaused.set(true);
                } else {
                    // Resume auto-refresh when no orders
                    if (!this.autoRefreshSubscription) {
                        this.startAutoRefresh();
                    }
                    this.isAutoRefreshPaused.set(false);
                }
            },
            error: (error) => {
                console.error('Error searching for orders:', error);
                this.isLoading.set(false);
                this.isAutoRefreshing.set(false);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Search Error',
                    detail: 'Failed to search for orders. Please try again.',
                });
            },
        });
    }

    private startAutoRefresh(): void {
        // Start auto-refresh every 5 seconds
        this.autoRefreshSubscription = interval(5000).subscribe(() => {
            this.isAutoRefreshing.set(true);
            this.searchForOrders();
        });
        this.isAutoRefreshPaused.set(false);
    }

    private enhanceOrdersWithDetails(orders: EnhancedOrder[]): void {
        orders.forEach((order) => {
            this.enhanceOrderDetails(order);
        });
    }

    private enhanceOrderDetails(order: EnhancedOrder): void {
        const requests: any[] = [];

        // Prepare requests for pricing and addresses
        if (order.pickupPoint && order.dropoffPoint) {
            // Create DTO for pricing calculation
            const priceDto: CreateOrderDto = {
                pickupLatitude: order.pickupPoint.latitude,
                pickupLongitude: order.pickupPoint.longitude,
                dropoffLatitude: order.dropoffPoint.latitude,
                dropoffLongitude: order.dropoffPoint.longitude,
            };

            // Add pricing request
            requests.push(
                this.services.OrderService.calculateOrderPrice(priceDto),
            );

            // Add pickup address request
            requests.push(
                this.services.NominatimService.reverseGeocode(
                    order.pickupPoint.longitude,
                    order.pickupPoint.latitude,
                ),
            );

            // Add dropoff address request
            requests.push(
                this.services.NominatimService.reverseGeocode(
                    order.dropoffPoint.longitude,
                    order.dropoffPoint.latitude,
                ),
            );

            // Execute all requests
            forkJoin(requests).subscribe({
                next: (responses: any[]) => {
                    const [
                        priceResponse,
                        pickupAddressResponse,
                        dropoffAddressResponse,
                    ] = responses;
                    // Update the order in the signal
                    this.availableOrders.update((orders) =>
                        orders.map((o) => {
                            if (o.id === order.id) {
                                return {
                                    ...o,
                                    estimatedPrice: priceResponse?.data || 0,
                                    pickupAddress: this.formatAddress(
                                        pickupAddressResponse,
                                    ),
                                    dropoffAddress: this.formatAddress(
                                        dropoffAddressResponse,
                                    ),
                                    isLoadingDetails: false,
                                };
                            }
                            return o;
                        }),
                    );
                },
                error: (error) => {
                    console.error('Error enhancing order details:', error);
                    // Mark as loading complete even if there's an error
                    this.availableOrders.update((orders) =>
                        orders.map((o) => {
                            if (o.id === order.id) {
                                return {
                                    ...o,
                                    isLoadingDetails: false,
                                };
                            }
                            return o;
                        }),
                    );
                },
            });
        }
    }

    private formatAddress(geocodingResult: any): string {
        if (!geocodingResult) return 'Unknown location';

        if (geocodingResult.display_name) {
            const address = geocodingResult.display_name;
            if (address.length > 60) {
                // Extract main parts for shorter display
                const parts = [];
                if (geocodingResult.address?.road)
                    parts.push(geocodingResult.address.road);
                if (geocodingResult.address?.neighbourhood)
                    parts.push(geocodingResult.address.neighbourhood);
                if (
                    geocodingResult.address?.city ||
                    geocodingResult.address?.town
                ) {
                    parts.push(
                        geocodingResult.address.city ||
                            geocodingResult.address.town,
                    );
                }
                return (
                    parts.slice(0, 2).join(', ') ||
                    address.substring(0, 60) + '...'
                );
            }
            return address;
        }

        return 'Unknown location';
    }

    private stopAutoRefresh(): void {
        if (this.autoRefreshSubscription) {
            this.autoRefreshSubscription.unsubscribe();
            this.autoRefreshSubscription = undefined;
        }
        this.isAutoRefreshing.set(false);
    }

    acceptOrder(order: EnhancedOrder): void {
        this.isLoading.set(true);
        this.services.OrderService.approveOrder(order.id).subscribe({
            next: (response) => {
                if (response.data) {
                    // Check if the response already contains the tripId
                    if (response.data.tripId) {
                        // Navigate immediately to trip info page
                        this.stopAutoRefresh();
                        this.services.Router.navigate([
                            '/main/trip',
                            response.data.tripId,
                        ]);
                    } else {
                        // Fallback: Start polling for trip ID if not immediately available
                        this.pollForTripId(order.id);
                    }
                }
                this.isLoading.set(false);
            },
            error: (error) => {
                console.error('Error accepting order:', error);
                this.isLoading.set(false);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Accept Failed',
                    detail: 'Failed to accept the order. Please try again.',
                });
            },
        });
    }

    private pollForTripId(orderId: string): void {
        let pollCount = 0;
        const maxPolls = 10;

        const pollSubscription = interval(3000).subscribe(() => {
            pollCount++;

            this.services.OrderService.getUserOrders().subscribe({
                next: (response) => {
                    if (response.data) {
                        const order = response.data.find(
                            (o) => o.id === orderId,
                        );
                        if (order && order.tripId) {
                            pollSubscription.unsubscribe();
                            this.stopAutoRefresh();
                            this.services.Router.navigate([
                                '/main/trip',
                                order.tripId,
                            ]);
                        } else if (pollCount >= maxPolls) {
                            pollSubscription.unsubscribe();
                            this.searchForOrders();
                        }
                    }
                },
                error: (error) => {
                    console.error('Error polling for trip ID:', error);
                    if (pollCount >= maxPolls) {
                        pollSubscription.unsubscribe();
                        this.searchForOrders();
                    }
                },
            });
        });
    }

    declineOrder(order: EnhancedOrder): void {
        // Simply remove from local list for now
        this.availableOrders.update((orders) => {
            const updatedOrders = orders.filter((o) => o.id !== order.id);

            // Resume auto-refresh if no orders left
            if (updatedOrders.length === 0 && !this.autoRefreshSubscription) {
                setTimeout(() => this.startAutoRefresh(), 1000); // Small delay to show empty state
            }

            return updatedOrders;
        });

        this.services.MessageService.add({
            severity: 'info',
            summary: 'Order Declined',
            detail: 'Order has been declined and removed from your list.',
        });
    }

    formatDate(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    formatTime(date: Date): string {
        return date.toLocaleTimeString();
    }

    calculateDistance(pickupPoint: any): string {
        const userLoc = this.userLocation();
        if (!userLoc || !pickupPoint) return 'N/A';

        const R = 6371; // Earth's radius in km
        const dLat =
            ((pickupPoint.latitude - userLoc.latitude) * Math.PI) / 180;
        const dLon =
            ((pickupPoint.longitude - userLoc.longitude) * Math.PI) / 180;
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos((userLoc.latitude * Math.PI) / 180) *
                Math.cos((pickupPoint.latitude * Math.PI) / 180) *
                Math.sin(dLon / 2) *
                Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;

        return distance < 1
            ? `${Math.round(distance * 1000)}m`
            : `${distance.toFixed(1)}km`;
    }

    formatLocation(point: any): string {
        if (!point) return 'Unknown location';
        return `${point.latitude.toFixed(4)}, ${point.longitude.toFixed(4)}`;
    }
}
