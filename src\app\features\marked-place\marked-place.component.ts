import { Component, computed, inject, signal } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { divIcon, latLng, LatLng, marker } from 'leaflet';
import { ConfirmationService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { PopoverModule } from 'primeng/popover';
import { MarkedPlace, MarkedPlaceService } from '../../services';
import { MapComponent } from '../../shared/components/map.component';
import { injectMany } from '../../shared/helpers/injectMany';
import { getRandomColor } from '../../shared/helpers/randomColor';

@Component({
    selector: 'app-marked-place',
    imports: [MapComponent, PopoverModule, RouterLink, ConfirmDialogModule],
    templateUrl: './marked-place.component.html',
})
export class MarkedPlaceComponent {
    services = injectMany({ MarkedPlaceService, Router });
    private confirmationService = inject(ConfirmationService);

    markedPlaces = signal<(MarkedPlace & { color: string })[]>([]);
    markedPlacesMap = computed(() => {
        return this.markedPlaces().map((v) => {
            return this.addMarkerOnClick(
                latLng(v.latitude, v.longitude),
                v.name,
                v.color || getRandomColor(),
            );
        });
    });

    ngOnInit(): void {
        this.getMarkedPlaces();
    }

    getMarkedPlaces() {
        this.services.MarkedPlaceService.getAllMarkedPlaces().subscribe(
            (val) => {
                if (val.data) {
                    const d = val.data.map((v) => ({
                        ...v,
                        color: getRandomColor(),
                    }));
                    this.markedPlaces.set(d);
                }
            },
        );
    }

    addMarkerOnClick(latlng: LatLng, name: string, color: string) {
        const newMarker = marker(latlng, {
            draggable: false,
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div class="flex size-10 items-center justify-center rounded-full bg-opacity-30" style="background-color:${color}4d">
                              <div class="flex size-8 items-center justify-center rounded-full text-white" style="background-color:${color}">
                              </div>
                          </div>`,
            }),
        }).bindPopup(() => {
            return `<div>${name}</div>`;
        });
        return newMarker;
    }

    editPlace(place: MarkedPlace): void {
        this.services.Router.navigate([
            '/main',
            'marked-places',
            'edit',
            place.id,
        ]);
    }

    deletePlace(place: MarkedPlace): void {
        this.confirmationService.confirm({
            message: `Are you sure you want to delete "${place.name}"?`,
            header: 'Delete Confirmation',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.services.MarkedPlaceService.deleteMarkedPlace(
                    place.id,
                ).subscribe((response) => {
                    if (response.data) {
                        // Remove the deleted place from the list
                        this.markedPlaces.update((places) =>
                            places.filter((p) => p.id !== place.id),
                        );
                    }
                });
            },
        });
    }
}
