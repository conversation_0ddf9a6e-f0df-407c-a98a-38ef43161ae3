import { Controller, Post, Body, UseGuards, Get, Param } from '@nestjs/common';
import { CollaborativeTripService } from './collaborative-trip.service';
import { CreateCollaborativeTripOfferDto } from './dto/create-collaborative-trip-offer.dto';
import { FindMatchingTripsDto } from './dto/find-matching-trips.dto';
import { BookOfferDto } from './dto/book-offer.dto';
import { DriverGuard } from '../common/guards/driver.guard';
import { GetUser } from '../common/decorators/get-user.decorator';
import { User } from '@prisma/client';

@Controller('api/collaborative-trips')
export class CollaborativeTripController {
  constructor(private readonly collaborativeTripService: CollaborativeTripService) {}

  @Post()
  @UseGuards(DriverGuard)
  createCollaborativeTripOffer(
    @Body() createDto: CreateCollaborativeTripOfferDto,
    @GetUser() driver: User,
  ) {
    return this.collaborativeTripService.createCollaborativeTripOffer(
      driver.id,
      createDto,
    );
  }

  @Get('my/recent')
  @UseGuards(DriverGuard)
  getMyRecentCollaborativeTripOffers(@GetUser() driver: User) {
    return this.collaborativeTripService.getMyRecentCollaborativeTripOffers(driver.id);
  }

  @Get('my/old')
  @UseGuards(DriverGuard)
  getMyOldCollaborativeTripOffers(@GetUser() driver: User) {
    return this.collaborativeTripService.getMyOldCollaborativeTripOffers(driver.id);
  }

  @Post('find-matching')
  findMatchingCollaborativeTripOffers(@Body() searchDto: FindMatchingTripsDto) {
    return this.collaborativeTripService.findMatchingCollaborativeTripOffers(searchDto);
  }

  @Post('book-offer/:offerId')
  bookOffer(
    @Param('offerId') offerId: string,
    @Body() bookDto: BookOfferDto,
    @GetUser() client: User,
  ) {
    return this.collaborativeTripService.bookOffer(offerId, bookDto, client.id);
  }
} 