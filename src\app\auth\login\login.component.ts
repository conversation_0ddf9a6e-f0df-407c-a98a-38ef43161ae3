import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { FloatLabelModule } from 'primeng/floatlabel';
import { PasswordModule } from 'primeng/password';
import { AuthService } from '../../services/auth.service';
import { RouterLink } from '@angular/router';
import { InputTextModule } from 'primeng/inputtext';
import { IftaLabelModule } from 'primeng/iftalabel';
@Component({
  selector: 'app-login',
  imports: [CommonModule,TranslateModule,
            PasswordModule,FloatLabelModule,
            ReactiveFormsModule,RouterLink,InputTextModule,IftaLabelModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit{

    fb = inject(FormBuilder)
    authService = inject(AuthService)
    loginForm !: FormGroup

    ngOnInit(): void {
        this.loginForm = this.fb.group({
            phone_number: [null,[Validators.required]],
            password: [null,[Validators.required,Validators.minLength(8)]],
        })
    }

    login(){
        const credentials ={phone_number:this.loginForm.get('phone_number')?.value.toString(),
                            password: this.loginForm.get('password')?.value
        }
        this.authService.logIn(credentials)
    }
}
