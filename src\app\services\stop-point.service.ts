import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    StopPoint,
    CreateStopPointDto,
    UpdateStopPointDto,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export type {
    StopPoint,
    CreateStopPointDto,
    UpdateStopPointDto,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class StopPointService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Get all stop points
     * @returns Observable with array of StopPoint objects
     */
    getAllStopPoints(): Observable<
        { data: StopPoint[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/stop-points',
            des: this.destroyRef,
        };
        return this.http.get<StopPoint[]>(options);
    }

    /**
     * Get a specific stop point by ID
     * @param stopPointId ID of the stop point
     * @returns Observable with StopPoint object
     */
    getStopPointById(
        stopPointId: string,
    ): Observable<
        { data: StopPoint; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: `api/stop-points/${stopPointId}`,
            des: this.destroyRef,
        };
        return this.http.get<StopPoint>(options);
    }

    /**
     * Create a new stop point
     * @param createStopPointDto Stop point creation data
     * @returns Observable with created StopPoint object
     */
    createStopPoint(
        createStopPointDto: CreateStopPointDto,
    ): Observable<
        { data: StopPoint; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/stop-points',
            opj: createStopPointDto,
            des: this.destroyRef,
        };
        return this.http.post<StopPoint>(options);
    }

    /**
     * Update a stop point
     * @param stopPointId ID of the stop point to update
     * @param updateStopPointDto Stop point update data
     * @returns Observable with updated StopPoint object
     */
    updateStopPoint(
        stopPointId: string,
        updateStopPointDto: UpdateStopPointDto,
    ): Observable<
        { data: StopPoint; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: `api/stop-points/${stopPointId}`,
            opj: updateStopPointDto,
            des: this.destroyRef,
        };
        return this.http.patch<StopPoint>(options);
    }

    /**
     * Delete a stop point
     * @param stopPointId ID of the stop point to delete
     * @returns Observable with deleted StopPoint object
     */
    deleteStopPoint(
        stopPointId: string,
    ): Observable<
        { data: StopPoint; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: `api/stop-points/${stopPointId}`,
            des: this.destroyRef,
        };
        return this.http.delete<StopPoint>(options);
    }
}
