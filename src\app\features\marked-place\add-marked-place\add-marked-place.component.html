<div class="bg-background-color-100 p-4 md:p-6 md:pl-20">
    <div class="mx-auto max-w-4xl">
        <div
            class="rounded-xl border border-background-color-300 bg-background-color-100 shadow-shadow-200"
        >
            <!-- Header -->
            <div class="border-b border-background-color-300 p-6">
                <div class="flex items-center gap-4">
                    <button
                        class="flex h-10 w-10 items-center justify-center rounded-lg border border-background-color-300 bg-background-color-100 text-text-color-300 transition-all duration-200 hover:bg-background-color-200 hover:text-text-color-100"
                        [routerLink]="['/main', 'marked-places']"
                    >
                        <i class="pi pi-arrow-left text-sm"></i>
                    </button>
                    <div>
                        <h1 class="text-2xl font-bold text-text-color-100">
                            Add Marked Place
                        </h1>
                        <p class="mt-1 text-text-color-300">
                            Create a new marked location on the map
                        </p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6">
                <!-- Place Name Form -->
                <form [formGroup]="placeForm" class="mb-6">
                    <div class="mb-6">
                        <label
                            for="name"
                            class="mb-2 block text-sm font-medium text-text-color-100"
                        >
                            Place Name
                        </label>
                        <input
                            id="name"
                            type="text"
                            pInputText
                            formControlName="name"
                            class="w-full rounded-lg border border-background-color-300 bg-background-color-100 px-4 py-3 text-text-color-100 placeholder:text-text-color-300 focus:border-main-color-600"
                            placeholder="Enter place name"
                        />
                    </div>
                </form>

                <!-- Location Search -->
                <div class="mb-6">
                    <label
                        class="mb-2 block text-sm font-medium text-text-color-100"
                    >
                        Search Location
                    </label>
                    <div
                        class="flex gap-3 rounded-lg border border-background-color-300 bg-background-color-100 p-2"
                    >
                        <div class="flex flex-1 items-center gap-3">
                            <i class="pi pi-search text-text-color-300"></i>
                            <p-autocomplete
                                class="flex-1"
                                styleClass="w-full border-none bg-transparent text-sm text-text-color-100 placeholder:text-text-color-300 focus:outline-none"
                                [ngModel]="search()"
                                [ngModelOptions]="{ standalone: true }"
                                (completeMethod)="search.set($event.query)"
                                [suggestions]="searchRes$() || []"
                                [optionLabel]="'display_name'"
                                [optionValue]="'latlng'"
                                (onSelect)="afterSearch($event.value)"
                                [appendTo]="'body'"
                                placeholder="Search for a location..."
                            />
                        </div>
                        <button
                            class="flex h-8 w-8 items-center justify-center rounded-md bg-main-color-600 text-background-color-100 transition-all duration-200 hover:bg-main-color-700"
                            (click)="pointGeoLocation()"
                            [pTooltip]="'Use current location'"
                        >
                            <i class="pi pi-crosshairs text-sm"></i>
                        </button>
                    </div>
                </div>

                <!-- Map Section -->
                <div class="mb-6">
                    <label
                        class="mb-2 block text-sm font-medium text-text-color-100"
                    >
                        Location on Map
                    </label>
                    <div
                        class="h-[300px] w-full overflow-hidden rounded-lg border-2 border-background-color-200 shadow-shadow-200"
                    >
                        <app-map
                            [options]="options()"
                            [nodes]="mapMarker()"
                            (clickMap)="handleMapClick($event)"
                            class="h-full w-full"
                        ></app-map>
                    </div>
                    <p class="mt-2 text-sm text-text-color-300">
                        <i class="pi pi-info-circle mr-1"></i>
                        Click on the map to select a location for your marked
                        place
                    </p>
                </div>

                <!-- Action Buttons -->
                <div
                    class="flex flex-col gap-3 border-t border-background-color-300 pt-6 sm:flex-row sm:justify-end"
                >
                    <button
                        class="flex items-center justify-center gap-2 rounded-lg border border-background-color-300 bg-background-color-100 px-6 py-3 text-text-color-100 transition-all duration-200 hover:bg-background-color-200"
                        [routerLink]="['/main', 'marked-places']"
                    >
                        <i class="pi pi-times text-sm"></i>
                        Cancel
                    </button>
                    <button
                        class="flex items-center justify-center gap-2 rounded-lg bg-main-color-600 px-6 py-3 text-background-color-100 shadow-shadow-200 transition-all duration-200 hover:bg-main-color-700 hover:shadow-shadow-400 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-main-color-600 disabled:hover:shadow-shadow-200"
                        [disabled]="placeForm.invalid || !selectedLocation()"
                        (click)="savePlace()"
                    >
                        <i class="pi pi-check text-sm"></i>
                        Save Place
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
