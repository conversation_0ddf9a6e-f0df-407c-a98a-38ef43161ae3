import { Directive, ElementRef, inject, Input } from '@angular/core';
import { check } from './check';
import { HttpService } from '../services/http.service';

@Directive({
  selector: '[appPermissions]',
  providers: [],
  standalone: true,
})
export class PermissionsDirective {
  @Input() set appPermissions(val: string) {
    if (!this.check(val)) {
      (this.el.nativeElement as HTMLElement).remove();
    }
  }

  http = HttpService;
  el = inject(ElementRef);
  check = check.bind(this);
}
