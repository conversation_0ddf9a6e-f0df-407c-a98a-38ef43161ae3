import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { latLng } from 'leaflet';

import { NearPickupLocationStepComponent } from './near-pickup-location-step.component';
import { ValhallaService } from '../../../services/valhala.service';
import { NominatimService } from '../../../services/nominatim.service';
import { StopPointService } from '../../../services/stop-point.service';
import { ScheduledTripData } from '../scheduled-trip-steps.component';

describe('NearPickupLocationStepComponent - Valhalla Integration', () => {
    let component: NearPickupLocationStepComponent;
    let fixture: ComponentFixture<NearPickupLocationStepComponent>;
    let mockValhallaService: jasmine.SpyObj<ValhallaService>;
    let mockNominatimService: jasmine.SpyObj<NominatimService>;
    let mockStopPointService: jasmine.SpyObj<StopPointService>;

    const mockValhallaResponse = {
        trip: {
            legs: [
                {
                    shape: 'encoded_polyline_string',
                    summary: {
                        length: 5.2,
                        time: 420
                    }
                }
            ]
        }
    };

    beforeEach(async () => {
        const valhallaSpy = jasmine.createSpyObj('ValhallaService', ['getDirections']);
        const nominatimSpy = jasmine.createSpyObj('NominatimService', ['reverseGeocode']);
        const stopPointSpy = jasmine.createSpyObj('StopPointService', ['getAllStopPoints']);

        await TestBed.configureTestingModule({
            imports: [NearPickupLocationStepComponent],
            providers: [
                { provide: ValhallaService, useValue: valhallaSpy },
                { provide: NominatimService, useValue: nominatimSpy },
                { provide: StopPointService, useValue: stopPointSpy }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(NearPickupLocationStepComponent);
        component = fixture.componentInstance;
        mockValhallaService = TestBed.inject(ValhallaService) as jasmine.SpyObj<ValhallaService>;
        mockNominatimService = TestBed.inject(NominatimService) as jasmine.SpyObj<NominatimService>;
        mockStopPointService = TestBed.inject(StopPointService) as jasmine.SpyObj<StopPointService>;

        // Setup default mocks
        mockStopPointService.getAllStopPoints.and.returnValue(of({ data: [], error: null }));
        mockNominatimService.reverseGeocode.and.returnValue(of({ display_name: 'Test Address' }));
        mockValhallaService.getDirections.and.returnValue(of(mockValhallaResponse));
    });

    it('should call Valhalla service when route needs to be updated', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        fixture.detectChanges();

        expect(mockValhallaService.getDirections).toHaveBeenCalled();
        
        const callArgs = mockValhallaService.getDirections.calls.mostRecent().args[0];
        expect(callArgs.locations).toHaveLength(2);
        expect(callArgs.costing).toBe('auto');
    });

    it('should include near pickup location in route when selected', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };
        const nearPickup = latLng(40.7200, -74.0100);

        fixture.componentRef.setInput('tripData', tripData);
        component.nearPickupLocation.set(nearPickup);
        fixture.detectChanges();

        expect(mockValhallaService.getDirections).toHaveBeenCalled();
        
        const callArgs = mockValhallaService.getDirections.calls.mostRecent().args[0];
        expect(callArgs.locations).toHaveLength(3);
        expect(callArgs.locations[0]).toEqual({ lat: nearPickup.lat, lon: nearPickup.lng });
    });

    it('should create route polylines from Valhalla response', () => {
        spyOn(component, 'createRouteFromValhalla').and.callThrough();
        
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        fixture.detectChanges();

        expect(component.createRouteFromValhalla).toHaveBeenCalledWith(mockValhallaResponse.trip.legs);
    });

    it('should handle Valhalla service errors gracefully', () => {
        mockValhallaService.getDirections.and.returnValue(throwError('Valhalla service error'));
        spyOn(component, 'createFallbackRoute').and.callThrough();
        
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        fixture.detectChanges();

        expect(component.createFallbackRoute).toHaveBeenCalled();
    });

    it('should create fallback route with dashed lines', () => {
        const locations = [
            { lat: 40.7128, lon: -74.0060 },
            { lat: 40.7589, lon: -73.9851 }
        ];

        component.createFallbackRoute(locations);

        const polylines = component.routePolylines();
        expect(polylines.length).toBe(1);
        // Note: In a real test, you'd check the polyline properties
        // but since we're using Leaflet objects, this is more complex
    });

    it('should update route when near pickup location changes', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        fixture.detectChanges();

        // Clear previous calls
        mockValhallaService.getDirections.calls.reset();

        // Change near pickup location
        const nearPickup = latLng(40.7200, -74.0100);
        component.nearPickupLocation.set(nearPickup);

        // Should trigger route update
        expect(mockValhallaService.getDirections).toHaveBeenCalled();
    });

    it('should include route polylines in map nodes', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        
        // Mock some route polylines
        const mockPolylines = [{ type: 'polyline', id: 'route1' }];
        component.routePolylines.set(mockPolylines);

        const nodes = component.mapNodes();
        
        // Should include the route polylines in the map nodes
        expect(nodes).toContain(mockPolylines[0]);
    });
});
