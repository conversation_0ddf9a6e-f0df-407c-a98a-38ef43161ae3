import {
    Component,
    OnInit,
    Signal,
    WritableSignal,
    inject,
    signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MapComponent } from '../map.component';
import { divIcon, latLng, LatLngTuple, Layer, Marker, polyline } from 'leaflet';
import { getRandomColor } from '../../helpers/randomColor';
import { ValhallaService } from '../../../services/valhala.service';
import { parseDirectionsGeometry } from '../../helpers/helpers';
import { DirectionsRequest } from '../../types/valhalla.types';

@Component({
    selector: 'app-trip-map-dialog',
    standalone: true,
    imports: [CommonModule, MapComponent],
    templateUrl: './trip-map-dialog.component.html',
})
export class TripMapDialogComponent implements OnInit {
    private config = inject(DynamicDialogConfig);
    private dialogRef = inject(DynamicDialogRef);
    private valhallaService = inject(ValhallaService);

    trip!: {
        dropoffPoint: { longitude: number; latitude: number };
        pickupPoint: { longitude: number; latitude: number };
    };
    mapMarkers: WritableSignal<Layer[]> = signal([]);

    ngOnInit(): void {
        this.trip = this.config.data.trip;

        this.mapMarkers.set([
            marker(
                this.trip.pickupPoint.latitude,
                this.trip.pickupPoint.longitude,
                'green',
            ),
            marker(
                this.trip.dropoffPoint.latitude,
                this.trip.dropoffPoint.longitude,
                'red',
            ),
        ]);
        this.drawLine();
    }

    close(): void {
        this.dialogRef.close();
    }

    drawLine() {
        const directionsRequest: DirectionsRequest = {
            locations: [
                {
                    lat: this.trip.pickupPoint.latitude,
                    lon: this.trip.pickupPoint.longitude,
                },
                {
                    lat: this.trip.dropoffPoint.latitude,
                    lon: this.trip.dropoffPoint.longitude,
                },
            ],
            costing: 'auto',
            directions_options: {
                units: 'kilometers',
                language: 'en',
            },
        };
        this.valhallaService
            .getDirections({
                ...directionsRequest,
            })
            .subscribe((val) => {
                const line: LatLngTuple[] = parseDirectionsGeometry(
                    val.trip.legs,
                );
                console.log(line);
                const r = polyline(line, {
                    color: 'blue',
                }).bindPopup(() => {
                    return val.trip.summary
                        ? `<div>Distance: ${val.trip.summary.length}km</div>
                           <div>Time: ${Math.round(val.trip.summary.time / 60)}min</div>`
                        : '';
                });
                this.mapMarkers.set([...this.mapMarkers(), r]);
            });
    }
}

const marker = (latitude: number, longitude: number, color: string) => {
    return new Marker(latLng(latitude, longitude), {
        draggable: false,
        icon: divIcon({
            iconSize: [36, 36],
            className: '',
            html: `<div class="flex size-10 items-center justify-center rounded-full bg-opacity-30" style="background-color:${color}4d">
                                                  <div class="flex size-8 items-center justify-center rounded-full text-white" style="background-color:${color}">
                                                  </div>
                                              </div>`,
        }),
    });
};
