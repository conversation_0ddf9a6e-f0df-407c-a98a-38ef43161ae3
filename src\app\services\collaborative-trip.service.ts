import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export type {
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class CollaborativeTripService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Create a new collaborative trip offer (driver only)
     * @param createDto Collaborative trip offer creation data
     * @returns Observable with created CollaborativeTripOffer data
     */
    createCollaborativeTripOffer(
        createDto: CreateCollaborativeTripOfferDto,
    ): Observable<
        | { data: CollaborativeTripOffer; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips',
            opj: createDto,
            des: this.destroyRef,
            successMessage: 'Collaborative trip offer created successfully',
            failedMessage: 'Failed to create collaborative trip offer',
        };
        return this.http.post<CollaborativeTripOffer>(options);
    }
}
