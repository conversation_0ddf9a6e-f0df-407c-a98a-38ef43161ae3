import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export type {
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class CollaborativeTripService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Create a new collaborative trip offer (driver only)
     * @param createDto Collaborative trip offer creation data
     * @returns Observable with created CollaborativeTripOffer data
     */
    createCollaborativeTripOffer(
        createDto: CreateCollaborativeTripOfferDto,
    ): Observable<
        { data: CollaborativeTripOffer; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips',
            opj: createDto,
            des: this.destroyRef,
            successMessage: 'Collaborative trip offer created successfully',
            failedMessage: 'Failed to create collaborative trip offer',
        };
        return this.http.post<CollaborativeTripOffer>(options);
    }

    /**
     * Get all available collaborative trip offers
     * @returns Observable with array of CollaborativeTripOffer objects
     */
    getAllCollaborativeTripOffers(): Observable<
        { data: CollaborativeTripOffer[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer[]>(options);
    }

    /**
     * Get a specific collaborative trip offer by ID
     * @param offerId ID of the collaborative trip offer
     * @returns Observable with CollaborativeTripOffer data
     */
    getCollaborativeTripOfferById(
        offerId: string,
    ): Observable<
        { data: CollaborativeTripOffer; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: `api/collaborative-trips/${offerId}`,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer>(options);
    }

    /**
     * Search for collaborative trip offers based on criteria
     * @param searchParams Search parameters for filtering offers
     * @returns Observable with array of matching CollaborativeTripOffer objects
     */
    searchCollaborativeTripOffers(
        searchParams: {
            startLatitude?: number;
            startLongitude?: number;
            endLatitude?: number;
            endLongitude?: number;
            scheduledDate?: string;
            maxDistance?: number;
            minSeats?: number;
            maxPrice?: number;
        },
    ): Observable<
        { data: CollaborativeTripOffer[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/search',
            opj: searchParams,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for search results
        };
        return this.http.post<CollaborativeTripOffer[]>(options);
    }

    /**
     * Join a collaborative trip offer (passenger)
     * @param offerId ID of the collaborative trip offer to join
     * @param joinData Join request data
     * @returns Observable with join confirmation
     */
    joinCollaborativeTripOffer(
        offerId: string,
        joinData: {
            pickupStopPointId: string;
            dropoffStopPointId: string;
            seatsRequested: number;
            passengerNotes?: string;
        },
    ): Observable<{ data: any; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/collaborative-trips/${offerId}/join`,
            opj: joinData,
            des: this.destroyRef,
            successMessage: 'Successfully joined collaborative trip',
            failedMessage: 'Failed to join collaborative trip',
        };
        return this.http.post<any>(options);
    }

    /**
     * Cancel a collaborative trip offer (driver only)
     * @param offerId ID of the collaborative trip offer to cancel
     * @returns Observable with cancellation confirmation
     */
    cancelCollaborativeTripOffer(
        offerId: string,
    ): Observable<{ data: any; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/collaborative-trips/${offerId}/cancel`,
            des: this.destroyRef,
            successMessage: 'Collaborative trip offer cancelled successfully',
            failedMessage: 'Failed to cancel collaborative trip offer',
        };
        return this.http.post<any>(options);
    }

    /**
     * Get collaborative trip offers created by the current driver
     * @returns Observable with array of driver's CollaborativeTripOffer objects
     */
    getMyCollaborativeTripOffers(): Observable<
        { data: CollaborativeTripOffer[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/my-offers',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer[]>(options);
    }

    /**
     * Get collaborative trips the current user has joined as a passenger
     * @returns Observable with array of joined CollaborativeTripOffer objects
     */
    getMyJoinedCollaborativeTrips(): Observable<
        { data: CollaborativeTripOffer[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/my-trips',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer[]>(options);
    }
}
