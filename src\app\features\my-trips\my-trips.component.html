<div class="grid">
    <div class="col-12">
        <h2 class="mb-4">My Trip</h2>

        <div class="grid">
            <div class="col-12 md:col-6 lg:col-4 mb-3">
                <p-card>
                    <ng-template pTemplate="header">
                        <div class="bg-primary p-3 text-white">
                            <div
                                class="justify-content-between align-items-center flex"
                            >
                                <h3 class="m-0">
                                    Trip #{{ order.id.substring(0, 8) }}
                                </h3>
                                <span
                                    class="rounded-lg px-2 py-1 text-sm font-medium"
                                >
                                    {{ order.status }}
                                </span>
                            </div>
                        </div>
                    </ng-template>

                    <div class="mb-3">
                        <div class="align-items-center mb-2 flex">
                            <i class="pi pi-calendar mr-2"></i>
                            <span>{{ formatDate(order.createdAt) }}</span>
                        </div>

                        <div class="align-items-center mb-2 flex">
                            <i class="pi pi-map-marker mr-2 text-green-500"></i>
                            <span class="font-semibold">From:</span>
                            @if (order.pickupPointId) {
                                <span class="ml-2"
                                    >Point ID: {{ order.pickupPointId }}</span
                                >
                            } @else {
                                <span class="ml-2">Unknown location</span>
                            }
                        </div>

                        <div class="align-items-center flex">
                            <i class="pi pi-flag-fill mr-2 text-red-500"></i>
                            <span class="font-semibold">To:</span>
                            @if (order.dropoffPointId) {
                                <span class="ml-2"
                                    >Point ID: {{ order.dropoffPointId }}</span
                                >
                            } @else {
                                <span class="ml-2">Unknown location</span>
                            }
                        </div>
                    </div>

                    <div class="justify-content-between flex">
                        <button
                            pButton
                            icon="pi pi-map"
                            label="View Details"
                            class="p-button-outlined"
                            (click)="viewTripDetails(order)"
                        ></button>

                        <button
                            pButton
                            icon="pi pi-times"
                            label="Cancel"
                            class="p-button-outlined p-button-danger"
                        ></button>
                    </div>
                </p-card>
            </div>
        </div>
    </div>
</div>
