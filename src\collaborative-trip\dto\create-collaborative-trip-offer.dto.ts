import { IsNumber, IsString, IsDateString, IsInt, IsPositive, IsA<PERSON>y, IsOptional, Min, Max } from 'class-validator';

export class CreateCollaborativeTripOfferDto {
  @IsDateString()
  scheduledTime: string;

  @IsNumber()
  @Min(-90)
  @Max(90)
  startLatitude: number;

  @IsNumber()
  @Min(-180)
  @Max(180)
  startLongitude: number;

  @IsNumber()
  @Min(-90)
  @Max(90)
  endLatitude: number;

  @IsNumber()
  @Min(-180)
  @Max(180)
  endLongitude: number;

  @IsArray()
  @IsOptional()
  requiredWaypoints?: Array<{  // array of points driver chosen to shape the route
    latitude: number;
    longitude: number;
  }>;

  @IsInt()
  @IsPositive()
  availableSeats: number;

  @IsNumber()
  @IsPositive()
  pricePerSeat: number;

  @IsArray()
  @IsOptional()
  possiblePickupPoints?: string[]; // Array of stop point IDs

  @IsArray()
  @IsOptional()
  possibleDropoffPoints?: string[]; // Array of stop point IDs

  @IsString()
  @IsOptional()
  driverNotes?: string;

  @IsArray()
  routePoints: Array<{  // array of route points from Valhalla
    lat: number;
    lng: number;
  }>;
} 