import { Modu<PERSON> } from '@nestjs/common';
import { TransactionController } from './transaction.controller';
import { TransactionService } from './transaction.service';
import { PrismaModule } from '../common/prisma/prisma.module';
import { JwtModule } from '@nestjs/jwt';
import { jwtConfig } from '../common/config/jwt.config';

@Module({
  imports: [PrismaModule, JwtModule.registerAsync(jwtConfig)],
  controllers: [TransactionController],
  providers: [TransactionService],
  exports: [TransactionService],
})
export class TransactionModule {}
