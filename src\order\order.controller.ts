import { Controller, Post, Body, UseGuards, Get, Param } from '@nestjs/common';
import { OrderService } from './order.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { DriverGuard } from '../common/guards/driver.guard';
import { GetUser } from '../common/decorators/get-user.decorator';
import { User, Order } from '@prisma/client';
import { CreateOrderDto } from './dto';
import { DriverLocationDto } from '../trip/dto';

@Controller('api/orders')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post('calculate-initial-order-price')
  @UseGuards(AuthGuard)
  getInitialOrderPrice(
    @Body() createOrderDto: CreateOrderDto,
  ): Promise<number> {
    return this.orderService.getInitialOrderPrice(createOrderDto);
  }

  @Post()
  @UseGuards(AuthGuard)
  create(
    @Body() createOrderDto: CreateOrderDto,
    @GetUser() user: User,
  ): Promise<Order> {
    return this.orderService.create(user.id, createOrderDto);
  }

  @Post('nearby')
  @UseGuards(DriverGuard)
  findNearbyPendingOrders(
    @Body() locationDto: DriverLocationDto,
    @GetUser() driver: User,
  ): Promise<Order[]> {
    return this.orderService.findNearbyPendingOrders(
      driver.id,
      locationDto.latitude,
      locationDto.longitude,
    );
  }

  @Post(':orderId/approve')
  @UseGuards(DriverGuard)
  approveOrder(
    @Param('orderId') orderId: string,
    @GetUser() driver: User,
  ): Promise<Order> {
    return this.orderService.approveOrder(orderId, driver.id);
  }
  @Post(':orderId/cancel')
  @UseGuards(DriverGuard)
  cancelOrder(
    @Param('orderId') orderId: string,
    @GetUser() driver: User,
  ): Promise<Order> {
    return this.orderService.cancelOrder(orderId, driver.id);
  }

  @Get('me')
  @UseGuards(AuthGuard)
  getUserOrders(@GetUser() user: User): Promise<Order[]> {
    return this.orderService.getUserOrders(user.id);
  }
  @Get('me/notComplete')
  @UseGuards(AuthGuard)
  getUserOrdersNotComplete(@GetUser() user: User): Promise<Order[]> {
    return this.orderService.getUserOrdersNotComplete(user.id);
  }
}
