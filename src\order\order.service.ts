import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { Order, OrderStatus, TripStatus } from '@prisma/client';
import { CreateOrderDto } from './dto';
import { PricingService, OrderWithPoints } from '../pricing/pricing.service';
import { log } from 'console';

@Injectable()
export class OrderService {
  private readonly MAX_DISTANCE_KM = 1; 
  private readonly MAX_NEARBY_ORDERS = 3;
  private readonly SUGGESTION_TIMEOUT_MINUTES = 2;

  constructor(
    private prisma: PrismaService,
    private pricingService: PricingService,
  ) {}

  async getInitialOrderPrice(data: CreateOrderDto): Promise<number> {
    // Create temporary point objects for pricing calculation (not persisted)
    const tempPickupPoint = {
      id: 'temp-pickup',
      latitude: data.pickupLatitude,
      longitude: data.pickupLongitude,
    };

    const tempDropoffPoint = {
      id: 'temp-dropoff',
      latitude: data.dropoffLatitude,
      longitude: data.dropoffLongitude,
    };

    // Create mock order object for pricing calculation
    const mockOrderForPricing = {
      pickupPoint: tempPickupPoint,
      dropoffPoint: tempDropoffPoint,
    };

    try {
      // Calculate initial price using the pricing service
      const initialPrice = await this.pricingService.calculateInitialPrice(
        mockOrderForPricing as OrderWithPoints,
      );

      return initialPrice;
    } catch (error) {
      console.error('Price calculation failed:', error);
      throw new BadRequestException('Failed to calculate price');
    }
  }

  async create(userId: string, data: CreateOrderDto): Promise<Order> {
    // Check if user has any non-completed orders
    const existingOrders = await this.prisma.order.findFirst({
      where: {
        userId,
        status: {
          in: [
            OrderStatus.PENDING,
            OrderStatus.SUGGESTED_FOR_DRIVER,
            OrderStatus.CONFIRMED,
          ],
        },
      },
    });

    if (existingOrders) {
      throw new BadRequestException(
        'You have pending orders that need to be completed first',
      );
    }

    try {
      const pickupPoint = await this.prisma.point.create({
        data: {
          latitude: data.pickupLatitude,
          longitude: data.pickupLongitude,
        },
      });

      const dropoffPoint = await this.prisma.point.create({
        data: {
          latitude: data.dropoffLatitude,
          longitude: data.dropoffLongitude,
        },
      });

      // Calculate initial price BEFORE creating the order
      const mockOrderForPricing = {
        pickupPoint: {
          id: pickupPoint.id,
          latitude: pickupPoint.latitude,
          longitude: pickupPoint.longitude,
        },
        dropoffPoint: {
          id: dropoffPoint.id,
          latitude: dropoffPoint.latitude,
          longitude: dropoffPoint.longitude,
        },
      };

      const initialPrice = await this.pricingService.calculateInitialPrice(
        mockOrderForPricing as OrderWithPoints,
      );

      // Create the order with initial price included
      const order = await this.prisma.order.create({
        data: {
          userId,
          status: OrderStatus.PENDING,
          pickupPointId: pickupPoint.id,
          dropoffPointId: dropoffPoint.id,
          initialPrice: initialPrice,
        },
        include: {
          pickupPoint: true,
          dropoffPoint: true,
        },
      });

      return order;
    } catch (error) {
      console.error('Order creation failed:', error);
      throw new BadRequestException('Failed to create order');
    }
  }

  async findNearbyPendingOrders(
    driverId: string,
    driverLat: number,
    driverLng: number,
  ): Promise<Order[]> {
    const timeoutThreshold = new Date();
    timeoutThreshold.setMinutes(
      timeoutThreshold.getMinutes() - this.SUGGESTION_TIMEOUT_MINUTES,
    );

    // Get all pending orders and timed out suggested orders with their pickup and dropoff points
    const availableOrders = await this.prisma.order.findMany({
      where: {
        AND: {
          userId: {
            not: driverId,
          },
          OR: [
            { status: OrderStatus.PENDING },
            {
              AND: [
                { status: OrderStatus.SUGGESTED_FOR_DRIVER },
                {
                  OR: [
                    { lastSuggestedAt: { gt: timeoutThreshold } },
                    { lastSuggestedAt: null },
                  ],
                },
              ],
            },
          ],
        },
      },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
      },
    });

    // Calculate distances and store orders with their distances
    const ordersWithDistances = availableOrders
      .map((order) => {
        let pickupLat: number | null = null;
        let pickupLng: number | null = null;

        if (order.pickupPoint) {
          pickupLat = order.pickupPoint.latitude;
          pickupLng = order.pickupPoint.longitude;
        }

        if (!pickupLat || !pickupLng) {
          return null;
        }

        const distance = this.calculateDistance(
          driverLat,
          driverLng,
          pickupLat,
          pickupLng,
        );

        return { order, distance };
      })
      .filter((item) => item !== null && item.distance <= this.MAX_DISTANCE_KM)
      .sort((a, b) => a!.distance - b!.distance)
      .slice(0, this.MAX_NEARBY_ORDERS);

    // Update status for selected orders and return them
    const updatedOrders = await Promise.all(
      ordersWithDistances.map(async (item) => {
        return this.prisma.order.update({
          where: { id: item!.order.id },
          data: {
            status: OrderStatus.SUGGESTED_FOR_DRIVER,
            lastSuggestedAt: new Date(),
            lastSuggestedDriverId: driverId,
          },
          include: {
            pickupPoint: true,
            dropoffPoint: true,
          },
        });
      }),
    );

    return updatedOrders;
  }

  private calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  async approveOrder(orderId: string, driverId: string): Promise<Order> {
    // Get the order and verify it exists
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
      },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Verify order status is SUGGESTED_FOR_DRIVER
    if (order.status !== OrderStatus.SUGGESTED_FOR_DRIVER) {
      throw new ConflictException('Order is not available for approval');
    }

    // Use transaction to ensure both order and trip are created atomically
    return this.prisma.$transaction(async (tx) => {
      // Create new trip with order relation
      const trip = await tx.trip.create({
        data: {
          driverId,
          status: TripStatus.DRIVER_DIDNT_ARRIVE,
          order: {
            connect: {
              id: orderId,
            },
          },
        },
      });

      // Update order status
      const updatedOrder = await tx.order.update({
        where: { id: orderId },
        data: {
          status: OrderStatus.CONFIRMED,
        },
        include: {
          trip: true,
        },
      });

      return updatedOrder;
    });
  }

  async cancelOrder(orderId: string, userId: string): Promise<Order> {
    // Get the order and verify it exists
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
        trip: true, // Include trip to check if it exists
      },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    if (order.status === OrderStatus.CANCLED) {
      return order;
    }

    if (
      order.status === OrderStatus.CONFIRMED ||
      order.status === OrderStatus.COMPLETED 
    ) {
      throw new ConflictException(
        'Order cannot be cancelled in its current status' + order.status,
      );
    }

    // Use transaction to ensure both order and trip are updated atomically
    return this.prisma.$transaction(async (tx) => {
      // If order has a trip, update trip status to CANCLED
      if (order.trip) {
        await tx.trip.update({
          where: { id: order.trip.id },
          data: {
            status: TripStatus.CANCLED,
          },
        });
      }

      // Update order status to CANCLED
      const updatedOrder = await tx.order.update({
        where: { id: orderId },
        data: {
          status: OrderStatus.CANCLED,
        },
        include: {
          pickupPoint: true,
          dropoffPoint: true,
          trip: true,
        },
      });

      return updatedOrder;
    });
  }

  async getUserOrders(userId: string): Promise<Order[]> {
    return this.prisma.order.findMany({
      where: {
        userId,
      },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
        trip: {
          include: {
            driver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phoneNumber: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
  async getUserOrdersNotComplete(userId: string): Promise<Order[]> {
    return this.prisma.order.findMany({
      where: {
        userId,
        status: {
          not: OrderStatus.COMPLETED,
        },
      },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
        trip: {
          include: {
            driver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phoneNumber: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}
