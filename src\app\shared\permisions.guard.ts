import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { HttpService } from '../services/http.service';

export const permisionsGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const http = inject(HttpService);

  const role = route.data['role'];
  if (!role) {
    return true;
  }
  if (check(http, role)) {
    return true;
  }
  router.navigate(['']);
  return false;
};
function check(http: HttpService, per: string) {
  return true;
  // const r = http.parmissions();
  // return r.includes(per);
}
