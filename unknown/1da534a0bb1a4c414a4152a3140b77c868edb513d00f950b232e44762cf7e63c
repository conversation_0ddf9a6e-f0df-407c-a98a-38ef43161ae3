import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
@Component({
    selector: 'app-auth',
    standalone: true,
    imports: [TranslateModule],
    templateUrl: './auth.component.html',
})
export class AuthComponent {

    translateService = inject(TranslateService);
    router = inject(Router);


}
