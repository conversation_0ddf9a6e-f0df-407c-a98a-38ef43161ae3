import { Routes } from '@angular/router';
import { AuthComponent } from './auth.component';

export const authRoutes : Routes = [
    { path: '', redirectTo: 'welcome', pathMatch: 'full'},
    { path:'welcome', loadComponent:() => import('./auth.component').then(
        (c) => c.AuthComponent),
      },
    { path:'sign-up', loadComponent:() => import('./signup/signup.component').then(
      (c) => c.SignupComponent),
    },
    { path:'login', loadComponent:() => import('./login/login.component').then(
        (c) => c.LoginComponent),
      },

  ];
