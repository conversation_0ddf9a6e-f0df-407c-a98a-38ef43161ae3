import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { StopPoint } from '@prisma/client';

@Injectable()
export class StopPointService {
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<StopPoint[]> {
    return this.prisma.stopPoint.findMany();
  }

  async findOne(id: string): Promise<StopPoint> {
    const stopPoint = await this.prisma.stopPoint.findUnique({
      where: { id },
    });

    if (!stopPoint) {
      throw new NotFoundException(`Stop point with ID ${id} not found`);
    }

    return stopPoint;
  }

  async create(data: {
    name: string;
    latitude: number;
    longitude: number;
  }): Promise<StopPoint> {
    return this.prisma.stopPoint.create({
      data,
    });
  }

  async update(
    id: string,
    data: {
      name?: string;
      latitude?: number;
      longitude?: number;
    },
  ): Promise<StopPoint> {
    await this.findOne(id); // Verify the stop point exists

    return this.prisma.stopPoint.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<StopPoint> {
    await this.findOne(id); // Verify the stop point exists

    return this.prisma.stopPoint.delete({
      where: { id },
    });
  }
}
