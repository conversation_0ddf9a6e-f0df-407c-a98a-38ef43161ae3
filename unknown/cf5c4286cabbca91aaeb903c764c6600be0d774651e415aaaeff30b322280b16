import { AbstractControl, ValidationErrors } from '@angular/forms';



export function uppercaseValidator(control: AbstractControl): ValidationErrors | null {
  const hasUppercase = /[A-Z]/.test(control.value);
  return hasUppercase ? null : { uppercase: true };
}

export function lowercaseValidator(control: AbstractControl): ValidationErrors | null {
  const hasLowercase = /[a-z]/.test(control.value);
  return hasLowercase ? null : { lowercase: true };
}

export function numberValidator(control: AbstractControl): ValidationErrors | null {
  const hasNumber = /\d/.test(control.value);
  return hasNumber ? null : { number: true };
}
