import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { MarkedPlace } from '@prisma/client';

@Injectable()
export class MarkedPlaceService {
  constructor(private prisma: PrismaService) {}

  async findAll(userId: string): Promise<MarkedPlace[]> {
    return this.prisma.markedPlace.findMany({
      where: { userId },
    });
  }

  async findOne(id: string, userId: string): Promise<MarkedPlace> {
    const markedPlace = await this.prisma.markedPlace.findUnique({
      where: { id },
    });

    if (!markedPlace) {
      throw new NotFoundException(`Marked place with ID ${id} not found`);
    }

    if (markedPlace.userId !== userId) {
      throw new ForbiddenException('Access to this marked place is forbidden');
    }

    return markedPlace;
  }

  async create(data: {
    name: string;
    latitude: number;
    longitude: number;
    userId: string;
  }): Promise<MarkedPlace> {
    return this.prisma.markedPlace.create({
      data,
    });
  }

  async update(
    id: string,
    userId: string,
    data: {
      name?: string;
      latitude?: number;
      longitude?: number;
    },
  ): Promise<MarkedPlace> {
    const markedPlace = await this.findOne(id, userId);

    return this.prisma.markedPlace.update({
      where: { id },
      data,
    });
  }

  async delete(id: string, userId: string): Promise<MarkedPlace> {
    const markedPlace = await this.findOne(id, userId);

    return this.prisma.markedPlace.delete({
      where: { id },
    });
  }
}
