import { inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, Observable, throwError } from 'rxjs';
import { User } from '../core/types/tripoos.types';
import { HttpService } from './http.service';

// Re-export types for convenience
export type {
    AuthResponse,
    RefreshTokenDto,
    SignInDto,
    SignUpDto,
    User,
    VerifyPhoneDto,
} from '../core/types/tripoos.types';

@Injectable({ providedIn: 'root' })
export class AuthService {
    httpService = inject(HttpService);
    router = inject(Router);

    user = signal<User | undefined | null>(undefined);

    /**
     * Register a new user
     * @param credentials User registration data
     * @returns Observable with registration response
     */
    signUp(credentials: any) {
        return this.httpService._post({
            link: 'api/users/auth/sign-up',
            opj: credentials,
        });
    }

    /**
     * Verify user's phone number with OTP code
     * @param phone_number User's phone number
     * @param code Verification code
     */
    verifyAccount(phone_number: String, code: string) {
        this.httpService
            ._post({
                link: 'api/users/auth/verify-phone',
                opj: { phone_number, code },
            })
            .subscribe((value) => {
                if (value.data) {
                    localStorage.setItem(
                        'Access-token',
                        value.data.access_token,
                    );
                    localStorage.setItem(
                        'Refresh-token',
                        value.data.refresh_token,
                    );
                    this.router.navigate(['main']);
                }
            });
    }

    sendOTP(phoneNumber: number) {
        this.httpService
            ._post({
                link: 'api/users/auth/send-verification-code',
                opj: { phone_number: phoneNumber },
            })
            .subscribe();
    }

    /**
     * Log in a user
     * @param credentials User login credentials
     */
    logIn(credentials: any) {
        this.httpService
            ._post({ link: 'api/users/auth/sign-in', opj: credentials })
            .subscribe((value) => {
                this.router.navigate(['main']);
                localStorage.setItem('Access-token', value.data.access_token);
                localStorage.setItem('Refresh-token', value.data.refresh_token);
            });
    }

    /**
     * Refresh the authentication token
     * @returns Observable with new tokens
     */
    refreshToken(): Observable<any> {
        const refreshToken = localStorage.getItem('Refresh-token');

        if (!refreshToken) {
            return throwError(() => new Error('No refresh token found'));
        }

        return this.httpService
            .post({
                link: 'api/users/auth/refresh',
                opj: { refresh_token: refreshToken },
            })
            .pipe(
                catchError((error) => {
                    return throwError(() => new Error(error));
                }),
            );
    }

    /**
     * Log out the current user
     */
    logOut() {
        localStorage.removeItem('Access-token');
        localStorage.removeItem('Refresh-token');
        this.user.set(null);
        this.router.navigate(['login']);
    }
}
