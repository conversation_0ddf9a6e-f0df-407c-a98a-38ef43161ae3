import { Component, signal, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { MoneyTransaction, TransactionService, TransactionStatus, TransactionType } from '../../services/transaction.service';
import { injectMany } from '../../shared/helpers/injectMany';
import { MessageService } from 'primeng/api';
import { UserService, DriverStatus } from '../../services/user.service';

@Component({
    selector: 'app-transactions',
    standalone: true,
    imports: [
        CommonModule,
        TableModule,
        CardModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        CalendarModule,
        DropdownModule,
        InputTextModule,
    ],
    templateUrl: './transactions.component.html'
})
export class TransactionsComponent {
    services = injectMany({
        TransactionService,
        MessageService,
        UserService,
    });

    // Make enums available in template
    TransactionType = TransactionType;
    TransactionStatus = TransactionStatus;
    DriverStatus = DriverStatus;

    transactions = signal<MoneyTransaction[]>([]);
    loading = signal<boolean>(false);
    selectedTransaction = signal<MoneyTransaction | null>(null);
    isDriver = signal<boolean>(false);

    // Filter options
    statusOptions = [
        { label: 'All', value: null },
        { label: 'Pending', value: TransactionStatus.PENDING },
        { label: 'Completed', value: TransactionStatus.COMPLETED },
        { label: 'Failed', value: TransactionStatus.FAILED },
    ];

    typeOptions = [
        { label: 'All', value: null },
        { label: 'Client to Driver', value: TransactionType.CLIENT_TO_DRIVER },
        { label: 'Driver to Company', value: TransactionType.DRIVER_TO_COMPANY },
    ];

    // Computed values for driver statistics
    totalIncome = computed(() => {
        if (!this.isDriver()) return 0;
        return this.transactions()
            .filter(t => t.status === TransactionStatus.COMPLETED && t.type === TransactionType.CLIENT_TO_DRIVER)
            .reduce((sum, t) => sum + t.amount, 0);
    });

    monthlyIncome = computed(() => {
        if (!this.isDriver()) return 0;
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        return this.transactions()
            .filter(t => {
                const transactionDate = new Date(t.createdAt);
                return t.status === TransactionStatus.COMPLETED && 
                       t.type === TransactionType.CLIENT_TO_DRIVER &&
                       transactionDate.getMonth() === currentMonth &&
                       transactionDate.getFullYear() === currentYear;
            })
            .reduce((sum, t) => sum + t.amount, 0);
    });

    pendingCompanyPayment = computed(() => {
        if (!this.isDriver()) return 0;
        return this.transactions()
            .filter(t => t.status === TransactionStatus.PENDING && t.type === TransactionType.DRIVER_TO_COMPANY)
            .reduce((sum, t) => sum + t.amount, 0);
    });

    ngOnInit(): void {
        this.checkDriverStatus();
        this.loadTransactions();
    }

    checkDriverStatus(): void {
        this.services.UserService.getProfile().subscribe({
            next: (response) => {
                if (response.data) {
                    this.isDriver.set(response.data.driverStatus === DriverStatus.APPROVED);
                }
            },
            error: () => {
                this.isDriver.set(false);
            }
        });
    }

    loadTransactions(): void {
        this.loading.set(true);
        this.services.TransactionService.getMyTransactions().subscribe({
            next: (response) => {
                if (response.data) {
                    this.transactions.set(response.data);
                } else {
                    this.services.MessageService.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to load transactions'
                    });
                }
                this.loading.set(false);
            },
            error: () => {
                this.loading.set(false);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load transactions'
                });
            }
        });
    }

    getStatusSeverity(status: TransactionStatus): 'success' | 'warn' | 'danger' | 'info' {
        switch (status) {
            case TransactionStatus.COMPLETED:
                return 'success';
            case TransactionStatus.PENDING:
                return 'warn';
            case TransactionStatus.FAILED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getTypeBadgeClass(type: TransactionType): string {
        switch (type) {
            case TransactionType.CLIENT_TO_DRIVER:
                return 'bg-blue-100 text-blue-800';
            case TransactionType.DRIVER_TO_COMPANY:
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    formatDate(dateString: string): string {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatCurrency(amount: number): string {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    refreshTransactions(): void {
        this.loadTransactions();
    }

    selectTransaction(transaction: MoneyTransaction): void {
        this.selectedTransaction.set(transaction);
    }
} 