import { IsNumber, IsInt, IsPositive, Min, Max } from 'class-validator';

export class FindMatchingTripsDto {
  @IsInt()
  @IsPositive()
  passengers: number;

  @IsNumber()
  @Min(-90)
  @Max(90)
  pickupLatitude: number;

  @IsNumber()
  @Min(-180)
  @Max(180)
  pickupLongitude: number;

  @IsNumber()
  @Min(-90)
  @Max(90)
  dropoffLatitude: number;

  @IsNumber()
  @Min(-180)
  @Max(180)
  dropoffLongitude: number;
} 