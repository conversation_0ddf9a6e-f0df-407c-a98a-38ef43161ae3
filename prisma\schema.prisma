generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model User {
  id                     String             @id @default(uuid())
  phoneNumber            String             @unique
  firstName              String
  lastName               String
  password               String
  isPhoneVerified        Boolean            @default(false)
  verificationCode       String?
  driverStatus           DriverStatus       @default(NONE)
  IdCardBackUrl          String?
  IdCardFrontUrl         String?
  PersonalPhotoUrl       String?
  verificationCodeSentAt DateTime?
  createdAt              DateTime           @default(now())
  isSuperAdmin           <PERSON>            @default(false)
  car                    Car?
  markedPlaces           MarkedPlace[]
  suggestedOrders        Order[]            @relation("SuggestedOrders")
  orders                 Order[]
  refreshTokens          RefreshToken[]
  transactionsFrom       MoneyTransaction[] @relation("TransactionsFrom")
  transactionsTo         MoneyTransaction[] @relation("TransactionsTo")
  trips                  Trip[]             @relation("DriverTrips")

  @@index([phoneNumber])
  @@index([driverStatus])
}

model Car {
  id           String     @id @default(uuid())
  userId       String     @unique
  make         String
  model        String
  year         Int
  licensePlate String?
  user         User       @relation(fields: [userId], references: [id])
  photos       CarPhoto[] @relation("CarPhotos")

  @@index([userId])
  @@index([make, model])
}

model CarPhoto {
  id       String @id @default(uuid())
  carId    String
  photoUrl String
  car      Car    @relation("CarPhotos", fields: [carId], references: [id])

  @@index([carId])
}

model StopPoint {
  id        String @id @default(uuid())
  name      String
  latitude  Float
  longitude Float
}

model MarkedPlace {
  id        String @id @default(uuid())
  userId    String
  name      String
  latitude  Float
  longitude Float
  user      User   @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Point {
  id            String  @id @default(uuid())
  latitude      Float
  longitude     Float
  dropoffOrders Order[] @relation("DropoffPoint")
  pickupOrders  Order[] @relation("PickupPoint")
}

model Order {
  id                    String             @id @default(uuid())
  userId                String
  status                OrderStatus        @default(PENDING)
  createdAt             DateTime           @default(now())
  pickupPointId         String?
  dropoffPointId        String?
  tripId                String?            @unique
  lastSuggestedAt       DateTime?
  lastSuggestedDriverId String?
  finalPrice            Float?
  initialPrice          Float?
  dropoffPoint          Point?             @relation("DropoffPoint", fields: [dropoffPointId], references: [id])
  lastSuggestedDriver   User?              @relation("SuggestedOrders", fields: [lastSuggestedDriverId], references: [id])
  pickupPoint           Point?             @relation("PickupPoint", fields: [pickupPointId], references: [id])
  trip                  Trip?              @relation(fields: [tripId], references: [id])
  user                  User               @relation(fields: [userId], references: [id])
  transactions          MoneyTransaction[]

  @@index([userId])
  @@index([pickupPointId])
  @@index([dropoffPointId])
  @@index([lastSuggestedDriverId])
  @@index([tripId])
}

model Trip {
  id                       String     @id @default(uuid())
  driverId                 String
  status                   TripStatus @default(DRIVER_DIDNT_ARRIVE)
  currentLocationLatitude  Float?
  currentLocationLongitude Float?
  createdAt                DateTime   @default(now())

  // Trip timing fields (in minutes)
  timeTillDriverArrived    Int? // Time from order creation to driver arrival
  timeDriverWaitingForClient Int? // Time driver waited for client
  actualTripTime           Int? // Time client was with driver (actual trip duration)

  // Car route tracking
  carRoute                 Json? // Array of points: [{lat: number, lng: number, timestamp: string}]

  order                    Order?
  driver                   User       @relation("DriverTrips", fields: [driverId], references: [id])

  @@index([driverId])
}



model MoneyTransaction {
  id          String            @id @default(uuid())
  type        TransactionType
  status      TransactionStatus @default(PENDING)
  amount      Float
  orderId     String
  fromUserId  String?
  toUserId    String?
  description String?
  createdAt   DateTime          @default(now())
  completedAt DateTime?
  fromUser    User?             @relation("TransactionsFrom", fields: [fromUserId], references: [id])
  order       Order             @relation(fields: [orderId], references: [id])
  toUser      User?             @relation("TransactionsTo", fields: [toUserId], references: [id])

  @@index([orderId])
  @@index([fromUserId])
  @@index([toUserId])
  @@index([type])
  @@index([status])
  @@map("Transaction")
}

enum DriverStatus {
  NONE
  IN_REVIEW
  PENDING_ONSITE_REVIEW
  REJECTED
  APPROVED
}

enum OrderStatus {
  PENDING
  SUGGESTED_FOR_DRIVER
  CONFIRMED
  COMPLETED
  CANCLED
}

enum TripStatus {
  DRIVER_DIDNT_ARRIVE
  DRIVER_WAITING_CLIENT
  DRIVER_WITH_CLIENT
  FINISHED
  CANCLED
}

enum TransactionType {
  CLIENT_TO_DRIVER
  DRIVER_TO_COMPANY
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
}
