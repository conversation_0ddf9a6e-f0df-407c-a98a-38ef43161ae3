<div class="p-4 min-h-[83dvh]">
    <p-toast></p-toast>


    @if (!showForm && driverStatus === 'NONE') {
        <div class="flex flex-col justify-between items-center h-full">
            <div class="flex flex-col gap-4 justify-center items-center text-center">
                <h1 class="m-0 text-4xl">{{ 'Become a Driver' | translate }}</h1>
                <img src="./images/become-driver.svg" alt="driver">
                <!-- <p class="font-bold text-base max-w-[319px]">{{ 'Complete the form below to apply as a driver. You’ll need to provide information about your vehicle and upload required documents.' }}</p> -->
                <p class="font-bold text-base max-w-[319px]">{{ 'Complete the form below to apply as a driver. You’ll need to provide information about your vehicle and upload required documents.' }}</p>
            </div>
            <button
            (click)="showForm = true"
            class="text-white text-center  w-full  max-w-[319px] p-4 border-none bg-blue-700 rounded-full hover:scale-105 duration-500 ease-in-out">{{ 'Apply' | translate }}</button>
        </div>
    }
    @if(driverStatus === 'IN_REVIEW'){
        <div class="flex flex-col items-center h-full justify-center gap-8">
            <div class="w-fit rounded-full bg-primary text-base px-8 py-4 font-semibold text-center">
                {{ 'in_review' | translate }}
            </div>

            <div class="w-[25rem] p-4 rounded-lg bg-primary-200">
                <h3 class="text-xl font-bold">{{ 'we_are_reviewing_your_application'  | translate}}</h3>
                <p class="text-base ">{{ 'thank you for providing the requested information, you will receive an email once we finish the review.'| translate }}</p>
                <h4 class="text-xl font-bold">{{ 'Estimated review time: 48 hour(s)' | translate}}</h4>
            </div>
        </div>
    }

    @if(driverStatus === 'PENDING_ONSITE_REVIEW'){
        <div class="flex flex-col items-center h-full justify-center gap-8">
            <div class="w-fit rounded-full bg-primary text-base px-8 py-4 font-semibold text-center">
                {{ 'ONSITE_REVIEW' | translate }}
            </div>

            <div class="w-[26rem] p-4 rounded-lg bg-primary-200">
                <h3 class="text-xl font-bold">{{ 'we_are_reviewing_your_application_on_site'  | translate}}</h3>
                <p class="text-base ">{{ 'thank you for providing the requested information, you will receive an email once we finish the review.'| translate }}</p>
                <h4 class="text-xl font-bold">{{ 'Estimated review time: 48 hour(s)' | translate}}</h4>
            </div>
        </div>
    }

    @if(!showForm && driverStatus === 'REJECTED'){
        <div class="flex flex-col items-center h-full justify-center gap-8 text-center">
            <div class="w-fit rounded-full bg-red-600 text-base px-8 py-4 font-semibold text-center">
                {{ 'rejected' | translate }}
            </div>

            <div class="w-[25rem] p-4 rounded-lg bg-primary-200">
                <h3 class="text-xl font-bold">{{ 'your_application_has_been_rejected'  | translate}}</h3>
            </div>
        </div>
    }
    @if (driverStatus==='APPROVED') {
        <div class="flex flex-col items-center h-full justify-center gap-8">
            <div class="w-fit rounded-full bg-primary text-base px-8 py-4 font-semibold text-center">
                {{ 'Approved' | translate }}
            </div>

            <div class="w-[25rem] p-4 rounded-lg bg-primary-200 text-center">
                <h3 class="text-xl font-bold">{{ 'your_application_has_been_approved'  | translate}}</h3>
            </div>
        </div>
    }
    @else if (showForm && driverStatus === 'NONE') {

        <div class="grid gap-8 mb-8">
            <div class="col-12 md:col-6">
                <p-card [header]="'Vehicle Information' | translate">
                    <form
                    [formGroup]="driverForm"
                    (ngSubmit)="onSubmit()"
                    class="p-fluid flex flex-col gap-2"
                >
                    <div class="field mb-4">
                        <label for="make" class="mb-2 block">Make *</label>
                        <input
                            class="w-full"
                            id="make"
                            type="text"
                            pInputText
                            formControlName="make"
                            placeholder="e.g. Toyota"
                        />
                        <small
                            *ngIf="
                                driverForm.get('make')?.invalid &&
                                driverForm.get('make')?.touched
                            "
                            class="p-error text-red-500"
                        >
                            Vehicle make is required
                        </small>
                    </div>

                    <div class="field mb-4">
                        <label for="model" class="mb-2 block">Model *</label>
                        <input
                            class="w-full"
                            id="model"
                            type="text"
                            pInputText
                            formControlName="model"
                            placeholder="e.g. Camry"
                        />
                        <small
                            *ngIf="
                                driverForm.get('model')?.invalid &&
                                driverForm.get('model')?.touched
                            "
                            class="p-error text-red-500"
                        >
                            Vehicle model is required
                        </small>
                    </div>

                    <div class="field mb-4">
                        <label for="year" class="mb-2 block">Year *</label>
                        <p-dropdown
                            id="year"
                            [options]="yearOptions"
                            formControlName="year"
                            [showClear]="false"
                            placeholder="Select Year"
                            styleClass="w-full"
                        ></p-dropdown>
                        <small
                            *ngIf="
                                driverForm.get('year')?.invalid &&
                                driverForm.get('year')?.touched
                            "
                            class="p-error text-red-500"
                        >
                            Valid year is required
                        </small>
                    </div>

                    <div class="field mb-4">
                        <label for="color" class="mb-2 block">Color *</label>
                        <input
                            class="w-full"
                            id="color"
                            type="text"
                            pInputText
                            formControlName="color"
                            placeholder="e.g. Black"
                        />
                        <small
                            *ngIf="
                                driverForm.get('color')?.invalid &&
                                driverForm.get('color')?.touched
                            "
                            class="p-error text-red-500"
                        >
                            Vehicle color is required
                        </small>
                    </div>

                    <div class="field mb-4">
                        <label for="licensePlate" class="mb-2 block"
                            >License Plate *</label
                        >
                        <input
                            class="w-full"
                            id="licensePlate"
                            type="text"
                            pInputText
                            formControlName="licensePlate"
                            placeholder="e.g. ABC123"
                        />
                        <small
                            *ngIf="
                                driverForm.get('licensePlate')?.invalid &&
                                driverForm.get('licensePlate')?.touched
                            "
                            class="p-error text-red-500"
                        >
                            License plate is required
                        </small>
                    </div>
                </form>
            </p-card>
        </div>

        <div class="col-12 md:col-6">
            <p-card header="Required Documents">

        <div class="mb-6">
            <label for="upload" class="text-sm mb-2">ID Card (Front) *</label>
            <div
  class="mt-2 border border-dashed border-main-color-500 rounded-sm p-5 text-center cursor-pointer"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event, 'idFront')"
  (click)="idFrontInput.click()"
  tabindex="0" (keyup)="idFrontInput.click()"
>
  <input
    type="file"
    #idFrontInput
    (change)="onFileSelect($event, 'idFront')"
    accept="image/*"
    hidden
  />
  <div class="flex items-center justify-center gap-2">
    @if(files.idFront.file){
        <img [src]="files.idFront.previewUrl" alt="ID Front Preview" class="max-h-20">
    }
    @else {
        <span class="text-sm ">Upload Image
        </span>
        <img src="./icons/upload-icon.svg" alt="upload">
    }


</div>
      @if(files.idFront.file){
      <span class="text-xs" (click)="removeFile('idFront')" tabindex="0" (keyup)="$event.preventDefault()">Click to <span class="!text-red-500 font-semibold">Delete</span> or drag and drop another
        <br>Image max size: 5MB</span>
    }
    @else {
        <span class="text-xs">Click to <span class="!text-blue-700 font-semibold">upload</span> or drag and drop an Image
          <br>max size: 5MB</span>
    }

            </div>
        </div>

        <div class="mb-6">
            <label for="upload" class="text-sm mb-2">ID Card (Back) *</label>
           <div
  class="mt-2 border border-dashed border-main-color-500 rounded-sm p-5 text-center cursor-pointer"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event, 'idBack')"
  (click)="idBackInput.click()"
  tabindex="0" (keyup)="idBackInput.click()"
>
  <input
    type="file"
    #idBackInput
    (change)="onFileSelect($event, 'idBack')"
    accept="image/*"
    hidden
  />
  <div class="flex items-center justify-center gap-2">
    @if(files.idBack.file){
        <img [src]="files.idBack.previewUrl" alt="ID Back Preview" class="max-h-20">
    }
    @else {
        <span class="text-sm">Upload Back ID</span>
        <img src="./icons/upload-icon.svg" alt="Upload">
    }
  </div>

  @if(files.idBack.file){
      <span class="text-xs" (click)="removeFile('idBack')" tabindex="0" (keyup)="$event.preventDefault()">
          Click to <span class="!text-red-500 font-semibold">Delete</span> or drag and drop another
          <br>Image max size: 5MB
      </span>
  }
  @else {
      <span class="text-xs">
          Click to <span class="!text-blue-700 font-semibold">upload</span> or drag and drop an Image
          <br>max size: 5MB
      </span>
  }
            </div>
        </div>

        <div class="mb-6">
            <label for="upload" class="text-sm mb-2">Personal Photo *</label>
            <div
  class="mt-2 border border-dashed border-main-color-500 rounded-sm p-5 text-center cursor-pointer"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event, 'personalPhoto')"
  (click)="personalPhotoInput.click()"
  tabindex="0" (keyup)="personalPhotoInput.click()"
>
  <input
    type="file"
    #personalPhotoInput
    (change)="onFileSelect($event, 'personalPhoto')"
    accept="image/*"
    hidden
  />
  <div class="flex items-center justify-center gap-2">
    @if(files.personalPhoto.file){
        <img [src]="files.personalPhoto.previewUrl" alt="Personal Photo Preview" class="max-h-20">
    }
    @else {
        <span class="text-sm">Upload Personal Photo</span>
        <img src="./icons/upload-icon.svg" alt="Upload">
    }
  </div>

  @if(files.personalPhoto.file){
      <span class="text-xs" (click)="removeFile('personalPhoto')" tabindex="0" (keyup)="$event.preventDefault()">
          Click to <span class="!text-red-500 font-semibold">Delete</span> or drag and drop another
          <br>Image max size: 5MB
      </span>
  }
  @else {
      <span class="text-xs">
          Click to <span class="!text-blue-700 font-semibold">upload</span> or drag and drop an Image
          <br>max size: 5MB
      </span>
  }
            </div>
        </div>

        <div class="mb-6">
            <label for="upload" class="text-sm mb-2">Car Photos * (Up to 10)</label>
            <div
  class="mt-2 border border-dashed border-main-color-500 rounded-sm p-5 text-center cursor-pointer"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event, 'carPhotos')"
  (click)="carPhotosInput.click()"
  tabindex="0" (keyup)="carPhotosInput.click()"
>
  <input
    type="file"
    #carPhotosInput
    (change)="onCarPhotosSelect($event)"
    accept="image/*"
    multiple
    hidden
  />
  <div class="flex flex-col items-center justify-center gap-4">
    @if(files.carPhotos.length > 0){
      <div class="flex flex-wrap gap-4">
         @for(photo of carPhotoPreviews; track $index; let i = $index){
           <div class="relative group">
            <img
              [src]="photo"
              alt="Car photo preview"
              class=" max-h-20 object-cover rounded-md border border-gray-200"
            >
            <button
              (click)="removeCarPhoto(i); $event.stopPropagation()"
              class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
              aria-label="Remove image"
            >
              ×
            </button>
          </div>
        }
      </div>
    }
    @else {
        <div class="flex items-center justify-center gap-2">
            <span class="text-sm">Upload Car Photos</span>
            <img src="./icons/upload-icon.svg" alt="Upload">
        </div>
    }
  </div>

  @if(files.carPhotos.length > 0){
      <span class="text-xs">
          Click to <span class="!text-blue-700 font-semibold">upload more</span> or drag and drop additional photos
          <br>Max 10 photos, 5MB each
      </span>
  }
  @else {
      <span class="text-xs">
          Click to <span class="!text-blue-700 font-semibold">upload</span> or drag and drop car photos
          <br>Max 10 photos, 5MB each
      </span>
  }
</div>
        </div>


            </p-card>
        </div>
    </div>

    <div class="mt-4 flex justify-center">
        <button
            pButton
            type="button"
            label="Submit Application"
            icon="pi pi-check"
            [disabled]="
            driverForm.invalid ||
            isSubmitting ||
            !files.idFront.file ||
            !files.idBack.file ||
            !files.personalPhoto.file ||
            files.carPhotos.length === 0
            "
            (click)="onSubmit()"
            [loading]="isSubmitting"
        ></button>
    </div>
    }

</div>
