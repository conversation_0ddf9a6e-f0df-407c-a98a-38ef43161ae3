<div class="profile-container">

    @if (isLoading()) {
        <div class="info-card">
            <div style="text-align: center; padding: 2rem; color: #666666;">
                <div style="font-size: 1.2rem; margin-bottom: 1rem;">Loading profile...</div>
                <div style="font-size: 2rem;">⏳</div>
            </div>
        </div>
    } @else if (user()) {
        <!-- User Information Card -->
        <div class="info-card">
            <h2 style="margin-top: 0; margin-bottom: 1.5rem; font-size: 1.5rem; color: #000000;">Personal Information</h2>
            
            <div class="info-row">
                <span class="info-label">Full Name</span>
                <span class="info-value">
                    @if (getUserFullName()) {
                        {{ getUserFullName() }}
                    } @else {
                        <span style="color: #666666;">Name not available from API</span>
                    }
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Phone Number</span>
                <span class="info-value">{{ user()?.phoneNumber }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Phone Verified</span>
                <span class="info-value">
                    @if (user()?.isPhoneVerified) {
                        <span style="color: #000000;">✓ Verified</span>
                    } @else {
                        <span style="color: #666666;">✗ Not Verified</span>
                    }
                </span>
            </div>
        </div>

        <!-- Driver Status Card -->
        <div class="info-card">
            <h2 style="margin-top: 0; margin-bottom: 1.5rem; font-size: 1.5rem; color: #000000;">Driver Status</h2>
            
            <div class="info-row">
                <span class="info-label">Current Status</span>
                <span class="status-tag" [class]="getDriverStatusClass(user()?.driverStatus!)">
                    {{ getDriverStatusText(user()?.driverStatus!) }}
                </span>
            </div>

            @if (user()?.car) {
                <div class="info-row">
                    <span class="info-label">Vehicle</span>
                    <span class="info-value">{{ user()?.car?.make }} {{ user()?.car?.model }} ({{ user()?.car?.year }})</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">License Plate</span>
                    <span class="info-value">{{ user()?.car?.licensePlate }}</span>
                </div>
            }

            @if (user()?.driverStatus === 'NONE') {
                <div style="margin-top: 2rem; text-align: center;">
                    <button class="action-button" (click)="toggleDriverForm()">
                        Apply to Become a Driver
                    </button>
                </div>
            } @else if (user()?.driverStatus === 'IN_REVIEW') {
                <div style="margin-top: 2rem; padding: 1.5rem; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
                    <h3 style="margin: 0 0 1rem 0; color: #856404;">Application Under Review</h3>
                    <p style="margin: 0; color: #856404;">
                        Thank you for submitting your driver application. We are currently reviewing your documents and information. 
                        You will receive an email notification once the review is complete.
                    </p>
                    <p style="margin: 1rem 0 0 0; font-weight: 600; color: #856404;">
                        Estimated review time: 24-48 hours
                    </p>
                </div>
            } @else if (user()?.driverStatus === 'PENDING_ONSITE_REVIEW') {
                <div style="margin-top: 2rem; padding: 1.5rem; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
                    <h3 style="margin: 0 0 1rem 0; color: #856404;">Pending Onsite Review</h3>
                    <p style="margin: 0; color: #856404;">
                        Your application has passed the initial review. We will contact you soon to schedule an onsite vehicle inspection.
                    </p>
                </div>
            } @else if (user()?.driverStatus === 'APPROVED') {
                <div style="margin-top: 2rem; padding: 1.5rem; background: #ffffff; border: 2px solid #000000; border-radius: 8px;">
                    <h3 style="margin: 0 0 1rem 0; color: #000000;">✓ Approved Driver</h3>
                    <p style="margin: 0; color: #000000;">
                        Congratulations! Your driver application has been approved. You can now start accepting ride requests.
                    </p>
                </div>
            } @else if (user()?.driverStatus === 'REJECTED') {
                <div style="margin-top: 2rem; padding: 1.5rem; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px;">
                    <h3 style="margin: 0 0 1rem 0; color: #721c24;">Application Rejected</h3>
                    <p style="margin: 0; color: #721c24;">
                        Unfortunately, your driver application has been rejected. Please contact support for more information.
                    </p>
                </div>
            }
        </div>

        <!-- Driver Application Form -->
        @if (showDriverForm()) {
            <div class="driver-form">
                <h2 style="margin-top: 0; margin-bottom: 2rem; font-size: 1.5rem; color: #000000;">Driver Application</h2>
                
                <form [formGroup]="driverForm" (ngSubmit)="onSubmitDriverApplication()">
                    <!-- Vehicle Information -->
                    <div style="margin-bottom: 2rem;">
                        <h3 style="margin-bottom: 1rem; color: #000000;">Vehicle Information</h3>
                        
                        <div class="form-field">
                            <label class="form-label">Make *</label>
                            <input type="text" class="form-input" formControlName="make" placeholder="e.g., Toyota">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">Model *</label>
                            <input type="text" class="form-input" formControlName="model" placeholder="e.g., Camry">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">Year *</label>
                            <select class="form-input" formControlName="year">
                                @for (year of yearOptions; track year) {
                                    <option [value]="year">{{ year }}</option>
                                }
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">Color *</label>
                            <input type="text" class="form-input" formControlName="color" placeholder="e.g., Black">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">License Plate *</label>
                            <input type="text" class="form-input" formControlName="licensePlate" placeholder="e.g., ABC123">
                        </div>
                    </div>

                    <!-- Document Upload -->
                    <div style="margin-bottom: 2rem;">
                        <h3 style="margin-bottom: 1rem; color: #000000;">Required Documents</h3>
                        
                        <!-- ID Front -->
                        <div class="form-field">
                            <label class="form-label">ID Card (Front) *</label>
                            <div class="file-upload" (click)="idFrontInput.click()">
                                <input type="file" #idFrontInput (change)="onFileSelect($event, 'idFront')" accept="image/*" hidden>
                                @if (files.idFront.previewUrl) {
                                    <img [src]="files.idFront.previewUrl" alt="ID Front" class="file-preview">
                                    <div style="margin-top: 1rem;">
                                        <button type="button" style="background: none; border: none; color: #666666; cursor: pointer; text-decoration: underline;" (click)="removeFile('idFront'); $event.stopPropagation();">
                                            Remove
                                        </button>
                                    </div>
                                } @else {
                                    <div style="color: #666666;">
                                        <div style="font-size: 2rem; margin-bottom: 1rem;">📄</div>
                                        <div>Click to upload ID front image</div>
                                        <div style="font-size: 0.9rem; margin-top: 0.5rem;">Max size: 5MB</div>
                                    </div>
                                }
                            </div>
                        </div>
                        
                        <!-- ID Back -->
                        <div class="form-field">
                            <label class="form-label">ID Card (Back) *</label>
                            <div class="file-upload" (click)="idBackInput.click()">
                                <input type="file" #idBackInput (change)="onFileSelect($event, 'idBack')" accept="image/*" hidden>
                                @if (files.idBack.previewUrl) {
                                    <img [src]="files.idBack.previewUrl" alt="ID Back" class="file-preview">
                                    <div style="margin-top: 1rem;">
                                        <button type="button" style="background: none; border: none; color: #666666; cursor: pointer; text-decoration: underline;" (click)="removeFile('idBack'); $event.stopPropagation();">
                                            Remove
                                        </button>
                                    </div>
                                } @else {
                                    <div style="color: #666666;">
                                        <div style="font-size: 2rem; margin-bottom: 1rem;">📄</div>
                                        <div>Click to upload ID back image</div>
                                        <div style="font-size: 0.9rem; margin-top: 0.5rem;">Max size: 5MB</div>
                                    </div>
                                }
                            </div>
                        </div>
                        
                        <!-- Personal Photo -->
                        <div class="form-field">
                            <label class="form-label">Personal Photo *</label>
                            <div class="file-upload" (click)="personalPhotoInput.click()">
                                <input type="file" #personalPhotoInput (change)="onFileSelect($event, 'personalPhoto')" accept="image/*" hidden>
                                @if (files.personalPhoto.previewUrl) {
                                    <img [src]="files.personalPhoto.previewUrl" alt="Personal Photo" class="file-preview">
                                    <div style="margin-top: 1rem;">
                                        <button type="button" style="background: none; border: none; color: #666666; cursor: pointer; text-decoration: underline;" (click)="removeFile('personalPhoto'); $event.stopPropagation();">
                                            Remove
                                        </button>
                                    </div>
                                } @else {
                                    <div style="color: #666666;">
                                        <div style="font-size: 2rem; margin-bottom: 1rem;">📸</div>
                                        <div>Click to upload personal photo</div>
                                        <div style="font-size: 0.9rem; margin-top: 0.5rem;">Max size: 5MB</div>
                                    </div>
                                }
                            </div>
                        </div>
                        
                        <!-- Car Photos -->
                        <div class="form-field">
                            <label class="form-label">Car Photos * (Up to 5)</label>
                            <div class="file-upload" (click)="carPhotosInput.click()">
                                <input type="file" #carPhotosInput (change)="onCarPhotosSelect($event)" accept="image/*" multiple hidden>
                                @if (files.carPhotos.length > 0) {
                                    <div style="display: flex; flex-wrap: wrap; gap: 1rem; justify-content: center;">
                                        @for (photo of files.carPhotos; track $index; let i = $index) {
                                            <div style="position: relative;">
                                                <img [src]="URL.createObjectURL(photo)" alt="Car Photo" class="file-preview">
                                                <button type="button" style="position: absolute; top: -8px; right: -8px; background: #000000; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 0.8rem;" (click)="removeCarPhoto(i); $event.stopPropagation();">
                                                    ×
                                                </button>
                                            </div>
                                        }
                                    </div>
                                    <div style="margin-top: 1rem; color: #666666;">
                                        {{ files.carPhotos.length }}/5 photos uploaded
                                    </div>
                                } @else {
                                    <div style="color: #666666;">
                                        <div style="font-size: 2rem; margin-bottom: 1rem;">🚗</div>
                                        <div>Click to upload car photos</div>
                                        <div style="font-size: 0.9rem; margin-top: 0.5rem;">Max 5 photos, 5MB each</div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div style="text-align: center; margin-top: 2rem;">
                        <button type="button" class="action-button" (click)="toggleDriverForm()" style="background: #666666; margin-right: 1rem;">
                            Cancel
                        </button>
                        <button type="submit" class="action-button" [disabled]="driverForm.invalid || isSubmitting() || !files.idFront.file || !files.idBack.file || !files.personalPhoto.file || files.carPhotos.length === 0">
                            @if (isSubmitting()) {
                                Submitting...
                            } @else {
                                Submit Application
                            }
                        </button>
                    </div>
                </form>
            </div>
        }
    } @else {
        <div class="info-card">
            <div style="text-align: center; padding: 2rem; color: #666666;">
                <div style="font-size: 1.2rem; margin-bottom: 1rem;">No user data available</div>
                <div style="font-size: 2rem;">⚠️</div>
                <button class="action-button" (click)="loadUserProfile()" style="margin-top: 1rem; background: #666666;">
                    Retry
                </button>
            </div>
        </div>
    }
</div>

<p-toast position="top-right"></p-toast> 