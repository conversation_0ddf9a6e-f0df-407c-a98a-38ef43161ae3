import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { DestroyRef, Injectable, inject } from '@angular/core';
import { catchError, finalize, map, takeUntil, tap } from 'rxjs/operators';
import { MessageService } from 'primeng/api';
import { LoadingService } from './loading.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, Subject, of } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

export class requestOptions {
    link = '';
    successToast?: boolean = false;
    failedToast?: boolean = true;
    successMessage?: string = 'request succeeded';
    failedMessage?: string = 'request faild';
    showloader?: boolean = true;
    opj?: any;
    des?: DestroyRef;
    extra?: any;
}

@Injectable({
    providedIn: 'root',
})
export class HttpService {
    server = 'http://localhost:3000/';

    cancle = new Subject();

    http = inject(HttpClient);
    message = inject(MessageService);
    loading = inject(LoadingService);
    destroyRef = inject(DestroyRef);
    translateService = inject(TranslateService);
    cancleRequests() {
        this.cancle.next(0);
    }
    constructor() {
        this.cancle.subscribe(() => {
            this.message.add({
                closable: true,
                severity: 'warn',
                sticky: true,
                summary: 'Warning',
                detail: 'Request has been canceled',
            });
            this.loading.$loading.next(0);
        });
    }

    attatchPipe<T>(stream: Observable<T>, op: requestOptions) {
        let error = false;
        if (op.showloader) this.loading.increse();
        if (!op.des) op.des = this.destroyRef;

        op.des.onDestroy(() => {
            if (op.showloader) this.loading.decrice();
        });
        return stream.pipe(
            takeUntilDestroyed(op.des),
            takeUntil(this.cancle),
            catchError((err: HttpErrorResponse) => {
                console.log(err);
                if (op.failedToast) this.showError(err.message + '');
                error = true;
                return of({ data: null, error: err });
            }),
            map((val) => {
                if (error) {
                    return val as { data: null; error: HttpErrorResponse };
                } else {
                    let translatedHeader =
                        this.translateService.instant('request succeeded');
                    if (op.successToast) this.showSuccess(translatedHeader);
                    return { data: val, error: null } as {
                        data: any;
                        error: null;
                    };
                }
            }),
            finalize(() => {
                if (op.showloader) this.loading.decrice();
            }),
        );
    }
    _attatchPipe(stream: Observable<any>, op: requestOptions) {
        let error = false;
        if (op.showloader) this.loading.increse();
        if (!op.des) op.des = this.destroyRef;

        op.des.onDestroy(() => {
            if (op.showloader) this.loading.decrice();
        });
        return stream.pipe(
            takeUntilDestroyed(op.des),
            takeUntil(this.cancle),
            catchError((err: HttpErrorResponse) => {
                if (op.failedToast) this.showError(err.error.message + '');
                error = true;
                return of({ data: null, error: err });
            }),
            map((val) => {
                if (error) {
                    return val as { data: null; error: HttpErrorResponse };
                } else {
                    let translatedHeader =
                        this.translateService.instant('request succeeded');
                    if (op.successToast) this.showSuccess(translatedHeader);
                    return { data: val, error: null } as {
                        data: any;
                        error: null;
                    };
                }
            }),
            finalize(() => {
                if (op.showloader) this.loading.decrice();
            }),
        );
    }

    get<T>(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.get<T>(this.server + op.link, op.extra);
        op.successToast = false;
        return this.attatchPipe(request, op);
    }
    post<T>(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.post<T>(
            this.server + op.link,
            op.opj,
            op.extra,
        );
        return this.attatchPipe(request, op);
    }
    delete<T>(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.delete<T>(this.server + op.link, op.extra);
        return this.attatchPipe(request, op);
    }
    put<T>(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.put<T>(
            this.server + op.link,
            op.opj,
            op.extra,
        );
        return this.attatchPipe(request, op);
    }
    patch<T>(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.patch<T>(
            this.server + op.link,
            op.opj,
            op.extra,
        );
        return this.attatchPipe(request, op);
    }
    _get(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.get(this.server + op.link, op.extra);
        return this._attatchPipe(request, op);
    }
    _post(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.post(this.server + op.link, op.opj, op.extra);
        return this._attatchPipe(request, op);
    }
    _delete(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.delete(this.server + op.link, op.extra);
        return this._attatchPipe(request, op);
    }
    _put(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.put(this.server + op.link, op.opj, op.extra);
        return this._attatchPipe(request, op);
    }
    _patch(op: requestOptions) {
        op = { ...new requestOptions(), ...op };
        const request = this.http.patch(
            this.server + op.link,
            op.opj,
            op.extra,
        );
        return this._attatchPipe(request, op);
    }

    showSuccess(massage: string | undefined) {
        let translatedHeader = 'Success';
        this.translateService.get('Success').subscribe((res) => {
            translatedHeader = res;
        });
        this.message.add({
            severity: 'success',
            summary: translatedHeader,
            detail: massage,
        });
    }
    showError(massage: string) {
        let translatedHeader = 'Error';
        this.translateService.get('Error').subscribe((res) => {
            translatedHeader = res;
        });
        this.message.add({
            severity: 'error',
            summary: translatedHeader,
            detail: massage,
        });
    }

    download(op: requestOptions) {
        return this.http.get(op.link, op.extra);
    }
}
