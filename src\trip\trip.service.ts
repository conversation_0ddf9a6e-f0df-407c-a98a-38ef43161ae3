import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { Trip, TripStatus, OrderStatus } from '@prisma/client';

@Injectable()
export class TripService {
  private readonly COMPANY_COMMISSION_RATE = 0.05; // 5% commission rate (configurable)

  constructor(private prisma: PrismaService) {}

  async markDriverArrived(tripId: string, driverId: string): Promise<Trip> {
    console.log('🚗 markDriverArrived called with:', { tripId, driverId });
    
    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: { order: true },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    console.log('✅ Trip found:', { tripId: trip.id, status: trip.status, driverId: trip.driverId });

    if (trip.driverId !== driverId) {
      throw new ConflictException('You are not assigned to this trip');
    }

    if (trip.status !== TripStatus.DRIVER_DIDNT_ARRIVE) {
      throw new ConflictException('Invalid trip status for this operation');
    }

    // Calculate time till driver arrived (from trip creation to driver arrival)
    const tripCreatedAt = trip.createdAt;
    const timeTillDriverArrived = Math.round((Date.now() - tripCreatedAt.getTime()) / 1000); // convert to seconds

    console.log('🔍 Time till driver arrived:', timeTillDriverArrived);
    console.log('🔍 Trip created at:', trip.createdAt);
    console.log('🔍 date now:', Date.now());


    return this.prisma.trip.update({
      where: { id: tripId },
      data: {
        status: TripStatus.DRIVER_WAITING_CLIENT,
        timeTillDriverArrived,
      },
      include: { order: true },
    });
  }

  async startTrip(tripId: string, driverId: string): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: { order: true },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    if (trip.driverId !== driverId) {
      throw new ConflictException('You are not assigned to this trip');
    }

    if (trip.status !== TripStatus.DRIVER_WAITING_CLIENT) {
      throw new ConflictException('Invalid trip status for this operation');
    }

    // Calculate time driver waited for client (from driver arrival to client pickup)
    const tripCreatedAt = trip.createdAt;
    const timeDriverWaitingForClient = Math.round((Date.now() - tripCreatedAt.getTime()) / 1000) - (trip.timeTillDriverArrived || 0);

    return this.prisma.trip.update({
      where: { id: tripId },
      data: {
        status: TripStatus.DRIVER_WITH_CLIENT,
        timeDriverWaitingForClient,
      },
      include: { order: true },
    });
  }

  async updateTripLocation(
    tripId: string,
    driverId: string,
    latitude: number,
    longitude: number,
  ): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: {
        id: tripId,
        driverId,
      },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found or not authorized');
    }

    if (trip.status === TripStatus.FINISHED) {
      throw new ConflictException('Cannot update location for finished trip');
    }

    // Get existing car route or initialize empty array
    const existingCarRoute = (trip.carRoute as Array<{lat: number, lng: number, timestamp: string}>) || [];
    
    // Add new point to car route
    const newRoutePoint = {
      lat: latitude,
      lng: longitude,
      timestamp: new Date().toISOString()
    };
    
    const updatedCarRoute = [...existingCarRoute, newRoutePoint];

    return this.prisma.trip.update({
      where: { id: tripId },
      data: {
        currentLocationLatitude: latitude,
        currentLocationLongitude: longitude,
        carRoute: updatedCarRoute,
      },
      select: {
        id: true,
        currentLocationLatitude: true,
        currentLocationLongitude: true,
        status: true,
        createdAt: true,
        driverId: true,
        timeTillDriverArrived: true,
        timeDriverWaitingForClient: true,
        actualTripTime: true,
        carRoute: true,
      },
    });
  }

  async getTripById(tripId: string): Promise<Trip> {
    console.log('🔍 Fetching trip with ID:', tripId);

    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: {
        order: {
          include: {
            pickupPoint: true,
            dropoffPoint: true,
            user: true,
          },
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
          },
        },
      },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    console.log('📋 Trip found:', {
      tripId: trip.id,
      orderId: trip.order?.id,
      userId: trip.order?.userId,
      hasUser: !!trip.order?.user,
      userData: trip.order?.user,
    });

    return trip;
  }

  async completeTrip(tripId: string, driverId: string): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: {
        order: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    if (trip.driverId !== driverId) {
      throw new ConflictException('You are not assigned to this trip');
    }

    if (trip.status !== TripStatus.DRIVER_WITH_CLIENT) {
      throw new ConflictException('Invalid trip status for this operation');
    }

    if (!trip.order) {
      throw new ConflictException('No order associated with this trip');
    }

    const finalPrice = trip.order.initialPrice || 0;

    // Calculate actual trip time (from client pickup to trip completion)
    // We need to find when the trip status was last updated to DRIVER_WITH_CLIENT
    const tripCreatedAt = trip.createdAt;
    const actualTripTime = Math.round((Date.now() - tripCreatedAt.getTime()) / 1000) - (trip.timeTillDriverArrived || 0) - (trip.timeDriverWaitingForClient || 0);

    return this.prisma.$transaction(async (tx) => {
      const updatedTrip = await tx.trip.update({
        where: { id: tripId },
        data: {
          status: TripStatus.FINISHED,
          actualTripTime,
        },
        include: { order: true },
      });

      await tx.order.update({
        where: { id: trip.order.id },
        data: {
          status: OrderStatus.COMPLETED,
          finalPrice: finalPrice,
        },
      });

      await tx.moneyTransaction.create({
        data: {
          type: 'CLIENT_TO_DRIVER',
          status: 'COMPLETED',
          amount: finalPrice,
          orderId: trip.order.id,
          fromUserId: trip.order.userId,
          toUserId: driverId,
          description: `Payment for trip ${tripId} - Client to Driver`,
        },
      });

      const companyCommission = finalPrice * this.COMPANY_COMMISSION_RATE;
      await tx.moneyTransaction.create({
        data: {
          type: 'DRIVER_TO_COMPANY',
          status: 'COMPLETED',
          amount: companyCommission,
          orderId: trip.order.id,
          fromUserId: driverId,
          toUserId: null,
          description: `Commission for trip ${tripId} - Driver to Company (${this.COMPANY_COMMISSION_RATE * 100}%)`,
        },
      });

      return updatedTrip;
    });
  }
}
