<div class="find-orders-container">
    <!-- Loading/Searching State -->
    @if (isSearching() && availableOrders().length === 0) {
        <div class="search-animation">
            <div class="search-pulse">
                <div class="search-ripple"></div>
                <div class="search-ripple delay-1"></div>
                <div class="search-ripple delay-2"></div>
                <i class="pi pi-search search-icon"></i>
            </div>
            <div class="search-text">Searching for orders...</div>
        </div>
    }

    <!-- Auto-refresh indicator -->
    @if (isAutoRefreshing()) {
        <div class="refresh-indicator">
            <i class="pi pi-refresh pi-spin"></i> Auto-refreshing...
        </div>
    }

    <!-- Orders Found -->
    @if (availableOrders().length > 0) {
        <div class="orders-section" style="padding: 2rem">
            <h2
                style="
                    margin-bottom: 2rem;
                    font-size: 2rem;
                    font-weight: 700;
                    color: #000;
                "
            >
                Available Orders
                <span
                    style="
                        font-size: 1rem;
                        font-weight: 400;
                        color: #666;
                        margin-left: 1rem;
                    "
                >
                    ({{ availableOrders().length }} found)
                </span>
            </h2>

            <div class="orders-grid">
                @for (order of availableOrders(); track order.id) {
                    <div
                        class="order-card fade-in"
                        [class.loading-shimmer]="isLoading()"
                    >
                        <!-- Order Header -->
                        <div class="order-header">
                            <div>
                                <div class="order-id">
                                    Order #{{ order.id.substring(0, 8) }}
                                </div>
                                <div class="order-time">
                                    {{ formatDate(order.createdAt) }}
                                </div>
                            </div>
                            <div class="order-header-right">
                                @if (order.isLoadingDetails) {
                                    <div class="price-badge loading-skeleton">
                                        0.00 SP
                                    </div>
                                } @else if (
                                    order.estimatedPrice !== undefined
                                ) {
                                    <div class="price-badge">
                                        {{ order.estimatedPrice.toFixed(2) }} SP
                                    </div>
                                }
                                <div class="distance-badge">
                                    {{ calculateDistance(order.pickupPoint) }}
                                </div>
                            </div>
                        </div>

                        <!-- Order Content -->
                        <div class="order-content">
                            <!-- Pickup Location -->
                            <div class="location-row">
                                <div class="location-icon pickup-icon">
                                    <i class="pi pi-map-marker"></i>
                                </div>
                                <div class="location-details">
                                    <div class="location-title">PICKUP</div>
                                    <div class="location-address">
                                        @if (order.isLoadingDetails) {
                                            <div class="loading-skeleton">
                                                Loading address...
                                            </div>
                                        } @else if (order.pickupAddress) {
                                            {{ order.pickupAddress }}
                                        } @else {
                                            {{
                                                formatLocation(
                                                    order.pickupPoint
                                                )
                                            }}
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Dropoff Location -->
                            <div class="location-row">
                                <div class="location-icon dropoff-icon">
                                    <i class="pi pi-flag-fill"></i>
                                </div>
                                <div class="location-details">
                                    <div class="location-title">
                                        DESTINATION
                                    </div>
                                    <div class="location-address">
                                        @if (order.isLoadingDetails) {
                                            <div class="loading-skeleton">
                                                Loading address...
                                            </div>
                                        } @else if (order.dropoffAddress) {
                                            {{ order.dropoffAddress }}
                                        } @else {
                                            {{
                                                formatLocation(
                                                    order.dropoffPoint
                                                )
                                            }}
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Order Actions -->
                            <div class="order-actions">
                                <button
                                    pButton
                                    label="Accept"
                                    icon="pi pi-check"
                                    class="accept-btn"
                                    (click)="acceptOrder(order)"
                                    [disabled]="isLoading()"
                                ></button>
                                <button
                                    pButton
                                    label="Decline"
                                    icon="pi pi-times"
                                    class="decline-btn"
                                    (click)="declineOrder(order)"
                                    [disabled]="isLoading()"
                                ></button>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }

    <!-- No Orders State -->
    @if (!isSearching() && availableOrders().length === 0) {
        <div class="search-animation">
            <div class="search-pulse">
                <div class="search-ripple"></div>
                <div class="search-ripple delay-1"></div>
                <div class="search-ripple delay-2"></div>
                <i class="pi pi-search search-icon"></i>
            </div>
            <div class="search-text">Scanning for nearby rides...</div>
            <div class="search-subtext">
                Your next passenger is just around the corner!
            </div>
        </div>
    }
</div>

<!-- Toast Messages -->
<p-toast position="top-right"></p-toast>
