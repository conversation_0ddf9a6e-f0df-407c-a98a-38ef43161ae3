import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FcmService } from '../../../services/fcm.service';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'app-notification',
    standalone: true,
    imports: [CommonModule, ToastModule, ButtonModule],
    template: `
        <div class="notification-container">
            <p-toast position="top-right"></p-toast>

            @if (
                fcmService.isSupported() &&
                (!notificationPermission ||
                    notificationPermission === 'default')
            ) {
                <div
                    class="notification-permission-banner align-items-center justify-content-between flex bg-primary p-3 text-white"
                >
                    <span
                        >Enable notifications to stay updated with your trips
                        and orders</span
                    >
                    <button
                        pButton
                        label="Enable"
                        icon="pi pi-bell"
                        class="p-button-sm p-button-outlined"
                        (click)="requestPermission()"
                    ></button>
                </div>
            }
        </div>
    `,
    styles: [
        `
            .notification-permission-banner {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            }
        `,
    ],
})
export class NotificationComponent implements OnInit {
    fcmService = inject(FcmService);
    private messageService = inject(MessageService);

    notificationPermission: NotificationPermission | null = null;

    ngOnInit(): void {
        this.checkPermission();
        this.setupMessageListener();
    }

    checkPermission(): void {
        this.fcmService.currentPermission$.subscribe((permission) => {
            this.notificationPermission = permission;
        });
    }

    requestPermission(): void {
        this.fcmService.requestPermission().then((granted) => {
            if (!granted) {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Notifications Blocked',
                    detail: 'Please enable notifications in your browser settings',
                });
            }
        });
    }

    setupMessageListener(): void {
        this.fcmService.currentMessage$.subscribe((message) => {
            if (message && message.notification) {
                this.messageService.add({
                    severity: 'info',
                    summary: message.notification.title || 'New Notification',
                    detail: message.notification.body || '',
                    life: 5000,
                });
            }
        });
    }
}
