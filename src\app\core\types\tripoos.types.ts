// Enums from Prisma schema
export enum DriverStatus {
    NONE = 'NONE',
    IN_REVIEW = 'IN_REVIEW',
    PENDING_ONSITE_REVIEW = 'PENDING_ONSITE_REVIEW',
    REJECTED = 'REJECTED',
    APPROVED = 'APPROVED',
}

export enum OrderStatus {
    PENDING = 'PENDING',
    SUGGESTED_FOR_DRIVER = 'SUGGESTED_FOR_DRIVER',
    CONFIRMED = 'CONFIRMED',
    COMPLETED = 'COMPLETED',
}

export enum TripStatus {
    DRIVER_DIDNT_ARRIVE = 'DRIVER_DIDNT_ARRIVE',
    DRIVER_WAITING_CLIENT = 'DRIVER_WAITING_CLIENT',
    DRIVER_WITH_CLIENT = 'DRIVER_WITH_CLIENT',
    FINISHED = 'FINISHED',
}

// Core entities from Prisma schema
export interface User {
    id: string;
    phoneNumber: string;
    firstName: string;
    lastName: string;
    password: string;
    isPhoneVerified: boolean;
    verificationCode?: string;
    verificationCodeSentAt?: string;
    driverStatus: DriverStatus;
    IdCardFrontUrl?: string;
    IdCardBackUrl?: string;
    PersonalPhotoUrl?: string;
    car?: Car;
    orders?: Order[];
    trips?: Trip[];
    markedPlaces?: MarkedPlace[];
    refreshTokens?: RefreshToken[];
    suggestedOrders?: Order[];
}

export interface Car {
    id: string;
    userId: string;
    make: string;
    model: string;
    year: number;
    licensePlate?: string;
    photos?: CarPhoto[];
    user?: User;
}

export interface CarPhoto {
    id: string;
    carId: string;
    photoUrl: string;
    car?: Car;
}

export interface Point {
    id: string;
    latitude: number;
    longitude: number;
}

export interface StopPoint {
    id: string;
    name: string;
    latitude: number;
    longitude: number;
}

export interface MarkedPlace {
    id: string;
    userId: string;
    name: string;
    latitude: number;
    longitude: number;
    user?: User;
}

export interface Order {
    id: string;
    userId: string;
    status: OrderStatus;
    createdAt: string;
    pickupPointId?: string;
    dropoffPointId?: string;
    lastSuggestedAt?: string;
    lastSuggestedDriverId?: string;
    tripId?: string;
    initialPrice?: number;
    finalPrice?: number;
    user?: User;
    pickupPoint?: Point;
    dropoffPoint?: Point;
    trip?: Trip;
    lastSuggestedDriver?: User;
}

export interface CarRoutePoint {
    lat: number;
    lng: number;
    timestamp: string;
}

export interface Trip {
    id: string;
    driverId: string;
    status: TripStatus;
    currentLocationLatitude?: number;
    currentLocationLongitude?: number;
    createdAt: string;
    timeTillDriverArrived?: number; // Time from order creation to driver arrival (minutes)
    timeDriverWaitingForClient?: number; // Time driver waited for client (minutes)
    actualTripTime?: number; // Time client was with driver (actual trip duration in minutes)
    carRoute?: CarRoutePoint[]; // Array of points: [{lat: number, lng: number, timestamp: string}]
    driver?: User;
    order?: Order;
}

export interface RefreshToken {
    id: string;
    token: string;
    userId: string;
    expiresAt: string;
    createdAt: string;
    user?: User;
}

// Collaborative Trip interfaces
export interface CollaborativeTripOffer {
    id: string;
    driverId: string;
    scheduledTime: string;
    startpoint: string;
    endpoint: string;
    routePoints: Array<{ lat: number; lng: number }>;
    totalTime: number;
    availableSeats: number;
    pricePerSeat: number;
    nearbyStopPoints: string[];
    possiblePickupPoints: string[];
    possibleDropoffPoints: string[];
    driverNotes?: string;
    createdAt: string;
    driver?: User;
    startPoint?: Point;
    endPoint?: Point;
    waypoints?: Point[];
}

export interface CreateCollaborativeTripOfferDto {
    scheduledTime: string;
    startLatitude: number;
    startLongitude: number;
    endLatitude: number;
    endLongitude: number;
    requiredWaypoints?: Array<{
        latitude: number;
        longitude: number;
    }>;
    availableSeats: number;
    pricePerSeat: number;
    possiblePickupPoints?: string[];
    possibleDropoffPoints?: string[];
    driverNotes?: string;
}

// DTOs for API requests
export interface SignUpDto {
    first_name: string;
    last_name: string;
    phone_number: string;
    password: string;
}

export interface SignInDto {
    phone_number: string;
    password: string;
}

export interface RefreshTokenDto {
    refresh_token: string;
}

export interface VerifyPhoneDto {
    phone_number: string;
    code: string;
}

export interface GetUsersDto {
    driverStatus?: DriverStatus;
}

export interface AnalyticsDto {
    start_date: string;
    end_date: string;
}

export interface CreateOrderDto {
    pickupLatitude: number;
    pickupLongitude: number;
    dropoffLatitude: number;
    dropoffLongitude: number;
}

export interface DriverLocationDto {
    latitude: number;
    longitude: number;
}

export interface CreateDriverDto {
    make: string;
    model: string;
    year: number;
    licensePlate?: string;
}

export interface DriverStatusUpdateDto {
    currentStatus: DriverStatus;
    newStatus: DriverStatus;
}

export interface CreateStopPointDto {
    name: string;
    latitude: number;
    longitude: number;
}

export interface UpdateStopPointDto {
    name?: string;
    latitude?: number;
    longitude?: number;
}

export interface CreateMarkedPlaceDto {
    name: string;
    latitude: number;
    longitude: number;
}

export interface UpdateMarkedPlaceDto {
    name?: string;
    latitude?: number;
    longitude?: number;
}

// Authentication response
export interface AuthResponse {
    access_token: string;
    refresh_token: string;
    user: User;
}

// File upload types
export interface DriverFiles {
    idFront?: File[];
    idBack?: File[];
    personalPhoto?: File[];
    carPhotos?: File[];
}

// User with car for admin views
export interface UserWithCar extends User {
    car?: Car;
}
