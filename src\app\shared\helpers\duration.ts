import { intervalToDuration } from 'date-fns';
export const formatDuration = (durationInSeconds: number) => {
    const duration = intervalToDuration({
        start: 0,
        end: durationInSeconds * 1000,
    });

    let durationStr = '';
    if (duration.days && duration.days > 0) {
        durationStr += duration.days + 'd ';
    }
    if (duration.hours && duration.hours > 0) {
        durationStr += duration.hours + 'h ';
    }
    if (duration.minutes && duration.minutes > 0) {
        durationStr += duration.minutes + 'm ';
    }
    if (duration.seconds && duration.seconds > 0) {
        durationStr += duration.seconds + 's';
    }
    return durationStr;
};
