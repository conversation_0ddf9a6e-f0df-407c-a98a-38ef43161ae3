<div class="layout-content-padded">
    <!-- <PERSON> Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Transactions</h1>
    </div>

    <!-- Driver Statistics (only show if user is approved driver) -->
    @if (isDriver()) {
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <!-- Total Income -->
            <p-card class="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <i class="pi pi-dollar text-2xl text-green-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-green-700">{{ formatCurrency(totalIncome()) }}</div>
                    <div class="text-sm text-green-600 font-medium">Total Income</div>
                </div>
            </p-card>

            <!-- Monthly Income -->
            <p-card class="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <i class="pi pi-calendar text-2xl text-blue-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-blue-700">{{ formatCurrency(monthlyIncome()) }}</div>
                    <div class="text-sm text-blue-600 font-medium">This Month</div>
                </div>
            </p-card>

            <!-- Pending Company Payment -->
            <p-card class="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <i class="pi pi-clock text-2xl text-orange-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-orange-700">{{ formatCurrency(pendingCompanyPayment()) }}</div>
                    <div class="text-sm text-orange-600 font-medium">Pending to Company</div>
                </div>
            </p-card>
        </div>
    }

    <!-- Transactions Cards -->
    <div class="space-y-4">
        @if (loading()) {
            <div class="flex justify-center items-center py-8">
                <i class="pi pi-spinner pi-spin text-2xl text-gray-400"></i>
            </div>
        } @else if (transactions().length === 0) {
            <div class="text-center py-8">
                <div class="flex flex-col items-center gap-3">
                    <i class="pi pi-receipt text-4xl text-gray-400"></i>
                    <p class="text-gray-500">No transactions found</p>
                </div>
            </div>
        } @else {
            @for (transaction of transactions(); track transaction.id) {
                <div class="mb-4">
                    <p-card 
                        class="hover:shadow-md transition-shadow duration-200">
                    <div class="flex flex-col space-y-3">
                        <!-- Top row: Amount and Status -->
                        <div class="flex justify-between items-start">
                            <div class="flex flex-col">
                                <span class="text-xl font-bold" 
                                      [ngClass]="{'text-green-600': transaction.type === TransactionType.CLIENT_TO_DRIVER, 'text-red-600': transaction.type === TransactionType.DRIVER_TO_COMPANY}">
                                    {{ transaction.type === TransactionType.CLIENT_TO_DRIVER ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
                                </span>
                                <span class="text-sm text-gray-600">{{ formatDate(transaction.createdAt) }}</span>
                            </div>
                            <p-tag 
                                [value]="transaction.status" 
                                [severity]="getStatusSeverity(transaction.status)">
                            </p-tag>
                        </div>

                        <!-- Transaction Type -->
                        <div class="flex items-center">
                            <span 
                                class="px-3 py-1 rounded-full text-sm font-medium"
                                [ngClass]="getTypeBadgeClass(transaction.type)">
                                {{ transaction.type === TransactionType.CLIENT_TO_DRIVER ? 'Client → Driver' : 'Driver → Company' }}
                            </span>
                        </div>

                        <!-- Description -->
                        @if (transaction.description) {
                            <div class="flex items-start space-x-2">
                                <span class="text-sm text-gray-500">Note:</span>
                                <span class="text-sm text-gray-700 flex-1">{{ transaction.description }}</span>
                            </div>
                        }

                        <!-- Participants (if available) -->
                        @if (transaction.fromUser || transaction.toUser) {
                            <div class="flex flex-col space-y-1 text-xs text-gray-600 border-t pt-2">
                                @if (transaction.fromUser) {
                                    <span>From: {{ transaction.fromUser.firstName }} {{ transaction.fromUser.lastName }}</span>
                                } @else {
                                    <span>From: Company</span>
                                }
                                @if (transaction.toUser) {
                                    <span>To: {{ transaction.toUser.firstName }} {{ transaction.toUser.lastName }}</span>
                                } @else {
                                    <span>To: Company</span>
                                }
                            </div>
                        }

                        <!-- Completed date (if available) -->
                        @if (transaction.completedAt) {
                            <div class="text-xs text-gray-500 border-t pt-2">
                                Completed: {{ formatDate(transaction.completedAt) }}
                            </div>
                        }
                                            </div>
                    </p-card>
                </div>
            }
        }
    </div>

    <!-- Transaction Details Sidebar (if a transaction is selected) -->
    @if (selectedTransaction()) {
        <div class="fixed inset-y-0 right-0 w-96 bg-white shadow-lg border-l border-gray-200 transform transition-transform duration-300 ease-in-out z-50">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold">Transaction Details</h3>
                    <button 
                        type="button" 
                        (click)="selectedTransaction.set(null)"
                        class="text-gray-400 hover:text-gray-600">
                        <i class="pi pi-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="p-4 space-y-4">
                <div>
                    <label class="text-sm font-medium text-gray-500">Transaction ID</label>
                    <p class="font-mono text-sm">{{ selectedTransaction()?.id }}</p>
                </div>
                
                <div>
                    <label class="text-sm font-medium text-gray-500">Amount</label>
                    <p class="text-lg font-bold" 
                       [ngClass]="{'text-green-600': selectedTransaction()?.type === TransactionType.CLIENT_TO_DRIVER, 'text-red-600': selectedTransaction()?.type === TransactionType.DRIVER_TO_COMPANY}">
                        {{ selectedTransaction()?.type === TransactionType.CLIENT_TO_DRIVER ? '+' : '-' }}{{ formatCurrency(selectedTransaction()?.amount || 0) }}
                    </p>
                </div>
                
                <div>
                    <label class="text-sm font-medium text-gray-500">Status</label>
                    <div>
                        <p-tag 
                            [value]="selectedTransaction()?.status || ''" 
                            [severity]="getStatusSeverity(selectedTransaction()?.status!)">
                        </p-tag>
                    </div>
                </div>
                
                <div>
                    <label class="text-sm font-medium text-gray-500">Type</label>
                    <p>{{ selectedTransaction()?.type === TransactionType.CLIENT_TO_DRIVER ? 'Client to Driver' : 'Driver to Company' }}</p>
                </div>
                
                <div>
                    <label class="text-sm font-medium text-gray-500">Order ID</label>
                    <p class="font-mono text-sm">{{ selectedTransaction()?.orderId }}</p>
                </div>
                
                @if (selectedTransaction()?.description) {
                    <div>
                        <label class="text-sm font-medium text-gray-500">Description</label>
                        <p>{{ selectedTransaction()?.description }}</p>
                    </div>
                }
                
                <div>
                    <label class="text-sm font-medium text-gray-500">Created</label>
                    <p>{{ formatDate(selectedTransaction()?.createdAt || '') }}</p>
                </div>
                
                @if (selectedTransaction()?.completedAt) {
                    <div>
                        <label class="text-sm font-medium text-gray-500">Completed</label>
                        <p>{{ formatDate(selectedTransaction()!.completedAt!) }}</p>
                    </div>
                }
            </div>
        </div>
    }
</div> 