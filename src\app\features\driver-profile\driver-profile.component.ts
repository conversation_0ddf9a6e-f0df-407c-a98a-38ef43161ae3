import {
    Component,
    DestroyRef,
    inject,
    On<PERSON><PERSON>roy,
    OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
    FormBuilder,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { FileUploadModule } from 'primeng/fileupload';
import { DropdownModule } from 'primeng/dropdown';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { DriverService, DriverFiles } from '../../services/driver.service';
import { Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { injectMany } from '../../shared/helpers/injectMany';
import { TranslateModule } from '@ngx-translate/core';
import { DriverStatus, UserService } from '../../services';

type SingleFileWithPreview = {
    file: File | null;
    previewUrl: string | null;
};

type FileCollection = {
    idFront: SingleFileWithPreview;
    idBack: SingleFileWithPreview;
    personalPhoto: SingleFileWithPreview;
    carPhotos: File[];
};

type SingleFileField = Exclude<keyof FileCollection, 'carPhotos'>;

@Component({
    selector: 'app-driver-profile',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CardModule,
        FileUploadModule,
        DropdownModule,
        ToastModule,
        TranslateModule,
    ],
    templateUrl: './driver-profile.component.html',
    styleUrl: './driver-profile.component.scss',
    providers: [MessageService],
})
export class DriverProfileComponent implements OnInit, OnDestroy {
    services = injectMany({
        FormBuilder,
        DriverService,
        MessageService,
        Router,
    });

    DestroyRef = inject(DestroyRef);
    userService = inject(UserService);

    showForm = false;
    driverStatus!: DriverStatus;
    driverForm: FormGroup;
    isSubmitting = false;
    currentYear = new Date().getFullYear();
    yearOptions: number[] = [];

    files: FileCollection = {
        idFront: { file: null, previewUrl: null },
        idBack: { file: null, previewUrl: null },
        personalPhoto: { file: null, previewUrl: null },
        carPhotos: [],
    };
    previewUrls: string[] = [];
    carPhotoPreviews: string[] = [];

    constructor() {
        for (let year = 1990; year <= this.currentYear; year++) {
            this.yearOptions.push(year);
        }

        this.driverForm = this.services.FormBuilder.group({
            make: ['', [Validators.required]],
            model: ['', [Validators.required]],
            year: [
                this.currentYear,
                [
                    Validators.required,
                    Validators.min(1900),
                    Validators.max(this.currentYear),
                ],
            ],
            color: ['', [Validators.required]],
            licensePlate: ['', [Validators.required]],
        });
    }

    ngOnInit(): void {
        this.userService.getProfile().subscribe((data) => {
            if (data.data) {
                this.driverStatus = data.data.driverStatus;
                console.log(this.driverStatus);
            }
        });
    }

    onDragOver(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
    }

    onDragLeave(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
    }

    onFileSelect(event: Event, field: SingleFileField): void {
        const input = event.target as HTMLInputElement;
        const files = input.files;

        if (files?.length) {
            if (this.files[field].previewUrl) {
                URL.revokeObjectURL(this.files[field].previewUrl!);
            }

            this.files[field] = {
                file: files[0],
                previewUrl: URL.createObjectURL(files[0]),
            };
        }
    }

    onCarPhotosSelect(event: Event): void {
        const input = event.target as HTMLInputElement;
        if (!input.files || this.files.carPhotos.length >= 10) return;

        const newFiles = Array.from(input.files);
        const remainingSlots = 10 - this.files.carPhotos.length;
        const filesToAdd = newFiles.slice(0, remainingSlots);

        const newPreviews = filesToAdd.map((file) => URL.createObjectURL(file));

        this.files.carPhotos = [...this.files.carPhotos, ...filesToAdd];
        this.carPhotoPreviews = [...this.carPhotoPreviews, ...newPreviews];
    }

    getCarPhotoPreview(file: File): string {
        return URL.createObjectURL(file);
    }

    onDrop(event: DragEvent, field: keyof FileCollection) {
        event.preventDefault();
        event.stopPropagation();

        if (!event.dataTransfer?.files) return;

        if (field === 'carPhotos') {
            this.files.carPhotos = [
                ...this.files.carPhotos,
                ...Array.from(event.dataTransfer.files),
            ];
        } else {
            this.onFileSelect(
                { target: { files: event.dataTransfer.files } } as any,
                field as SingleFileField,
            );
        }
    }

    removeFile(type: 'idFront' | 'idBack' | 'personalPhoto'): void {
        this.files[type].file = null;
        this.files[type].previewUrl = null;
    }

    removeCarPhoto(index: number): void {
        URL.revokeObjectURL(this.carPhotoPreviews[index]);
        this.carPhotoPreviews.splice(index, 1);
        this.files.carPhotos.splice(index, 1);
    }

    onSubmit(): void {
        if (this.driverForm.invalid) {
            this.services.MessageService.add({
                severity: 'error',
                summary: 'Form Error',
                detail: 'Please fill all required fields correctly',
            });
            return;
        }

        if (
            !this.files.idFront.file ||
            !this.files.idBack.file ||
            !this.files.personalPhoto.file ||
            this.files.carPhotos.length === 0
        ) {
            this.services.MessageService.add({
                severity: 'error',
                summary: 'Missing Files',
                detail: 'Please upload all required documents',
            });
            return;
        }

        this.isSubmitting = true;

        const driverData = this.driverForm.value;
        const files: DriverFiles | any = {
            idFront: this.files.idFront.file,
            idBack: this.files.idBack.file,
            personalPhoto: this.files.personalPhoto.file,
            carPhotos: this.files.carPhotos,
        };

        this.services.DriverService.becomeDriver(driverData, files).subscribe({
            next: (response) => {
                if (response.data) {
                    this.driverStatus = DriverStatus.IN_REVIEW;

                    setTimeout(() => {
                        this.services.Router.navigate(['/driver/profile']);
                    }, 2000);
                } else if (response.error) {
                    this.services.MessageService.add({
                        severity: 'error',
                        summary: 'Submission Failed',
                        detail:
                            response.error.message ||
                            'Failed to submit driver application',
                    });
                    this.isSubmitting = false;
                }
            },
        });
    }

    ngOnDestroy() {
        this.carPhotoPreviews.forEach((url) => URL.revokeObjectURL(url));
    }
}
