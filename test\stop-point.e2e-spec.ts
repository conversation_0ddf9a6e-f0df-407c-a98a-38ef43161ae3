import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/common/prisma/prisma.service';
import { User } from '@prisma/client';

describe('StopPoint (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let authToken: string;
  let testUser: User;

  beforeAll(async () => {
    try {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      app = moduleFixture.createNestApplication();
      app.useGlobalPipes(new ValidationPipe());
      prisma = app.get<PrismaService>(PrismaService);
      await app.init();

      // Clean up ALL related tables in the correct order
      await prisma.$transaction([
        prisma.order.deleteMany(),
        prisma.trip.deleteMany(),
        prisma.markedPlace.deleteMany(),
        prisma.stopPoint.deleteMany(),
        prisma.refreshToken.deleteMany(),
        prisma.user.deleteMany(),
      ]);

      // First sign up the user
      const signUpResponse = await request(app.getHttpServer())
        .post('/api/users/auth/sign-up')
        .send({
          first_name: 'Test',
          last_name: 'User',
          phone_number: '+**********',
          password: 'password123'
        });

      // Verify the phone number
      await request(app.getHttpServer())
        .post('/api/users/auth/verify-phone')
        .send({
          phone_number: '+**********',
          code: signUpResponse.body.verificationCode
        });

      // Now sign in
      const signInResponse = await request(app.getHttpServer())
        .post('/api/users/auth/sign-in')
        .send({
          phone_number: '+**********',
          password: 'password123'
        });

      if (!signInResponse.body.access_token) {
        throw new Error(`Sign in failed: ${JSON.stringify(signInResponse.body)}`);
      }

      authToken = signInResponse.body.access_token;
      
      // Get the user details
      testUser = await prisma.user.findUnique({
        where: { phoneNumber: '+**********' }
      });

    } catch (error) {
      console.error('Setup failed:', error);
      throw error;
    }
  });

  beforeEach(async () => {
    await prisma.stopPoint.deleteMany();
  });

  afterAll(async () => {
    await prisma.stopPoint.deleteMany();
    await prisma.refreshToken.deleteMany();
    await prisma.user.deleteMany();
    await prisma.$disconnect();
    await app.close();
  });

  describe('POST /api/stop-points', () => {
    it('should create a stop point', () => {
      return request(app.getHttpServer())
        .post('/api/stop-points')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Central Station',
          latitude: 51.5074,
          longitude: -0.1278
        })
        .expect(201)
        .expect(({ body }) => {
          expect(body.name).toBe('Central Station');
          expect(body.latitude).toBe(51.5074);
          expect(body.longitude).toBe(-0.1278);
        });
    });

    it('should fail without auth token', () => {
      return request(app.getHttpServer())
        .post('/api/stop-points')
        .send({
          name: 'Central Station',
          latitude: 51.5074,
          longitude: -0.1278
        })
        .expect(401);
    });
  });

  describe('GET /api/stop-points', () => {
    beforeEach(async () => {
      await prisma.stopPoint.createMany({
        data: [
          {
            name: 'Central Station',
            latitude: 51.5074,
            longitude: -0.1278
          },
          {
            name: 'North Station',
            latitude: 51.5074,
            longitude: -0.1278
          }
        ]
      });
    });

    it('should return all stop points', () => {
      return request(app.getHttpServer())
        .get('/api/stop-points')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect(({ body }) => {
          expect(body).toHaveLength(2);
          expect(body[0].name).toBe('Central Station');
          expect(body[1].name).toBe('North Station');
        });
    });
  });

  describe('GET /api/stop-points/:id', () => {
    let stopPoint;

    beforeEach(async () => {
      stopPoint = await prisma.stopPoint.create({
        data: {
          name: 'Central Station',
          latitude: 51.5074,
          longitude: -0.1278
        }
      });
    });

    it('should return a specific stop point', () => {
      return request(app.getHttpServer())
        .get(`/api/stop-points/${stopPoint.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect(({ body }) => {
          expect(body.id).toBe(stopPoint.id);
          expect(body.name).toBe('Central Station');
        });
    });

    it('should return 404 for non-existent stop point', () => {
      return request(app.getHttpServer())
        .get('/api/stop-points/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
