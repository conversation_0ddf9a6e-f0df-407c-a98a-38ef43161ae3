# Authentication Flow Documentation

## Endpoints Overview (`/api/users`)

### 1. Sign Up
**Endpoint**: `POST /api/users/auth/sign-up`
```json
{
  "first_name": "string",
  "last_name": "string",
  "phone_number": "string",
  "password": "string"
}
```
- Creates new unverified user account
- Generates 6-digit verification code
- Returns verification code (in production, this should be sent via SMS)

### 2. Phone Verification
**Endpoint**: `POST /api/users/auth/verify-phone`
```json
{
  "phone_number": "string",
  "code": "string"
}
```
- Verifies phone number using the code
- Sets `isPhoneVerified` to true
- Returns authentication tokens upon successful verification

### 3. Sign In
**Endpoint**: `POST /api/users/auth/sign-in`
```json
{
  "phone_number": "string",
  "password": "string"
}
```
Returns:
```json
{
  "access_token": "string",
  "refresh_token": "string"
}
```
- Validates credentials
- Only verified users can sign in
- Returns access and refresh tokens

### 4. Token Refresh
**Endpoint**: `POST /api/users/auth/refresh`
```json
{
  "refresh_token": "string"
}
```
Returns:
```json
{
  "access_token": "string",
  "refresh_token": "string"
}
```
- Validates refresh token
- Issues new access and refresh tokens
- Invalidates old refresh token

### 5. Get Profile
**Endpoint**: `GET /api/users/me`
- Requires authentication
- Returns current user profile

## Token System

### Access Token
- JWT format
- Contains user information:
  - User ID
  - Phone number
  - Verification status
  - Driver status (if applicable)
- Expires in 2 days
- Used for API authentication

### Refresh Token
- 40-character random string
- Stored in database
- One-time use only
- Used to obtain new access tokens
- Invalidated after use

## Authentication Guards

### 1. Auth Guard (`AuthGuard`)
- Validates access token
- Ensures phone is verified
- Used for general user authentication
- Adds user object to request

### 2. Driver Guard (`DriverGuard`)
- Extends Auth Guard functionality
- Additional check for driver status
- Only allows `APPROVED` drivers
- Used for driver-specific endpoints

## Security Flow

1. **Initial Authentication**
   - User signs up → Unverified account created
   - Verifies phone → Account activated
   - Signs in → Receives tokens

2. **API Requests**
   - Client includes access token in Authorization header
   - Format: `Bearer <access_token>`
   - Guard validates token and permissions

3. **Token Refresh**
   - When access token expires
   - Use refresh token to get new pair
   - Old refresh token invalidated

4. **Security Measures**
   - Phone verification required
   - Password hashing using bcrypt
   - One-time use refresh tokens
   - Token expiration
   - Driver status verification

## Error Handling

- `UnauthorizedException`: Invalid or missing token
- `BadRequestException`: Invalid credentials
- `ConflictException`: Phone already registered
- `ForbiddenException`: Insufficient permissions

## Database Schema

### User Table
```sql
- id (UUID)
- phoneNumber (unique)
- firstName
- lastName
- password (hashed)
- isPhoneVerified (boolean)
- verificationCode
- driverStatus (enum)
```

### RefreshToken Table
```sql
- id (UUID)
- token (unique)
- userId (foreign key)
- expiresAt
- createdAt
```

## Best Practices

1. **Token Storage**
   - Access token: Client memory
   - Refresh token: Secure HTTP-only cookie
   - Never store in localStorage

2. **Security Headers**
   - Use HTTPS only
   - Set appropriate CORS policies
   - Implement rate limiting

3. **Phone Verification**
   - Limited attempts
   - Time-based expiration
   - Secure code transmission

4. **Password Policy**
   - Minimum length: 8 characters
   - Require mixed case and numbers
   - Hash using bcrypt (salt rounds: 5)