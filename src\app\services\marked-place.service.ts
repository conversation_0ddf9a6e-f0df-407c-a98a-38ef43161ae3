import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    MarkedPlace,
    CreateMarkedPlaceDto,
    UpdateMarkedPlaceDto,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export type {
    MarkedPlace,
    CreateMarkedPlaceDto,
    UpdateMarkedPlaceDto,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class MarkedPlaceService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Get all marked places for the current user
     * @returns Observable with array of MarkedPlace objects
     */
    getAllMarkedPlaces(): Observable<
        { data: MarkedPlace[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/marked-places',
            des: this.destroyRef,
        };
        return this.http.get<MarkedPlace[]>(options);
    }

    /**
     * Get a specific marked place by ID
     * @param markedPlaceId ID of the marked place
     * @returns Observable with MarkedPlace object
     */
    getMarkedPlaceById(
        markedPlaceId: string,
    ): Observable<
        { data: MarkedPlace; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: `api/marked-places/${markedPlaceId}`,
            des: this.destroyRef,
        };
        return this.http.get<MarkedPlace>(options);
    }

    /**
     * Create a new marked place
     * @param createMarkedPlaceDto Marked place creation data
     * @returns Observable with created MarkedPlace object
     */
    createMarkedPlace(
        createMarkedPlaceDto: CreateMarkedPlaceDto,
    ): Observable<
        { data: MarkedPlace; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/marked-places',
            opj: createMarkedPlaceDto,
            des: this.destroyRef,
        };
        return this.http.post<MarkedPlace>(options);
    }

    /**
     * Update an existing marked place
     * @param markedPlaceId ID of the marked place to update
     * @param updateMarkedPlaceDto Marked place update data
     * @returns Observable with updated MarkedPlace object
     */
    updateMarkedPlace(
        markedPlaceId: string,
        updateMarkedPlaceDto: UpdateMarkedPlaceDto,
    ): Observable<
        { data: MarkedPlace; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: `api/marked-places/${markedPlaceId}`,
            opj: updateMarkedPlaceDto,
            des: this.destroyRef,
        };
        return this.http.patch<MarkedPlace>(options);
    }

    /**
     * Delete a marked place
     * @param markedPlaceId ID of the marked place to delete
     * @returns Observable with success/error response
     */
    deleteMarkedPlace(
        markedPlaceId: string,
    ): Observable<{ data: any; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/marked-places/${markedPlaceId}`,
            des: this.destroyRef,
        };
        return this.http.delete(options);
    }
}
