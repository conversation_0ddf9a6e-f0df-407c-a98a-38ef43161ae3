import { Component, DestroyRef, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { Image } from 'primeng/image';
import { ConfirmationService, MessageService } from 'primeng/api';
import { injectMany } from '../../shared/helpers/injectMany';
import {
    DriverService,
    DriverStatusUpdateDto,
} from '../../services/driver.service';
import { DriverStatus, User, UserService } from '../../services/user.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UserWithCar } from '../../models/user-with-car.dto';
import { environment } from '../../../environments/environment';

@Component({
    selector: 'app-drivers-evaluation',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        ToastModule,
        ConfirmDialogModule,
        Image,
    ],
    templateUrl: './drivers-evaluation.component.html',
    styles: `
        :host {
            display: block;
            padding: 1rem;
        }
    `,
    providers: [MessageService, ConfirmationService],
})
export class DriversEvaluationComponent implements OnInit {
    imagesLink = 'http://localhost:3000';
    services = injectMany({
        UserService,
        DriverService,
    });
    destroyRef = inject(DestroyRef);

    drivers = signal<UserWithCar[]>([]);

    ngOnInit(): void {
        this.loadDrivers();
    }

    /**
     * Load drivers with PENDING or IN_REVIEW status
     */
    loadDrivers(): void {
        // First load PENDING drivers
        this.services.UserService.getAllUsers(DriverStatus.IN_REVIEW)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((response) => {
                if (response.data) {
                    this.drivers.set(response.data);
                }
            });
    }

    /**
     * Accept a driver application
     * @param driver The driver to approve
     */
    acceptDriver(driver: User): void {
        const statusUpdate: DriverStatusUpdateDto = {
            currentStatus: driver.driverStatus,
            newStatus: DriverStatus.APPROVED,
        };

        this.services.DriverService.updateDriverStatus(driver.id, statusUpdate)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((response) => {
                if (response.data) {
                    // Remove the driver from the list
                    this.drivers.update((drivers) =>
                        drivers.filter((d) => d.id !== driver.id),
                    );
                }
            });
    }

    /**
     * Reject a driver application
     * @param driver The driver to reject
     */
    rejectDriver(driver: User): void {
        const statusUpdate: DriverStatusUpdateDto = {
            currentStatus: driver.driverStatus,
            newStatus: DriverStatus.REJECTED,
        };

        this.services.DriverService.updateDriverStatus(driver.id, statusUpdate)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((response) => {
                if (response.data) {
                    // Remove the driver from the list
                    this.drivers.update((drivers) =>
                        drivers.filter((d) => d.id !== driver.id),
                    );
                }
            });
    }

    /**
     * View a document in a dialog or new window
     * @param documentUrl URL of the document to view
     */
    viewDocument(documentUrl: string): void {
        // Open the document in a new tab/window
        window.open(documentUrl, '_blank');
    }

    /**
     * Handle image loading errors by setting a fallback image
     * @param event The error event
     */
    onImageError(event: any): void {
        const imgElement = event.target;
        imgElement.src = 'assets/images/profile-placeholder.png';
    }
}
