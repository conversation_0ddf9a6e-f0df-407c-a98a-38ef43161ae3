import { Component, inject, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { NgxSpinnerComponent } from 'ngx-spinner';
import { ConfirmDialog } from 'primeng/confirmdialog';
import { Toast } from 'primeng/toast';
import { ThemeService } from './app/services/theme.service';

@Component({
    selector: 'app-root',
    standalone: true,
    imports: [RouterModule, Toast, NgxSpinnerComponent, ConfirmDialog],
    template: `<router-outlet />
        <p-toast> </p-toast>
        <ngx-spinner
            [name]="'main'"
            type="ball-clip-rotate-pulse"
        ></ngx-spinner>
        <p-confirmDialog [closable]="false"></p-confirmDialog> `,
})
export class AppComponent implements OnInit {
    translate = inject(TranslateService);
    themeService = inject(ThemeService);
    constructor() {
        this.translate.addLangs(['ar', 'en']);
        this.translate.setDefaultLang('en');
        this.translate.use('en');
    }

    ngOnInit(): void {
        this.themeService.initializeTheme();
    }
}
