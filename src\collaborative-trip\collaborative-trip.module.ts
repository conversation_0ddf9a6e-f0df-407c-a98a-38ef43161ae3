import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { CollaborativeTripController } from './collaborative-trip.controller';
import { CollaborativeTripService } from './collaborative-trip.service';
import { PrismaModule } from '../common/prisma/prisma.module';
import { PricingModule } from '../pricing/pricing.module';

@Module({
  imports: [PrismaModule, PricingModule, JwtModule.register({})],
  controllers: [CollaborativeTripController],
  providers: [CollaborativeTripService],
  exports: [CollaborativeTripService],
})
export class CollaborativeTripModule {} 