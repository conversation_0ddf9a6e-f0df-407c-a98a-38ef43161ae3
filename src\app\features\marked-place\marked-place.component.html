<div class="relative flex h-full w-full flex-col bg-background-color-100">
    <!-- Top Bar with Tags -->
    <div
        class="z-[500] border-b border-background-color-300 bg-background-color-100 p-4"
    >
        <div class="mb-4 ms-8 flex items-center justify-between">
            <h1 class="text-xl font-semibold text-text-color-100">
                Marked Places
            </h1>
            <button
                class="flex items-center gap-2 rounded-lg bg-black px-4 py-2 text-background-color-100 shadow-shadow-200 transition-all duration-200 hover:bg-main-color-700 hover:shadow-shadow-300"
                routerLink="add"
            >
                <i class="pi pi-plus text-sm"></i>
                <span class="font-medium">Add Place</span>
            </button>
        </div>

        <!-- Places Tags -->
        @if (markedPlaces().length > 0) {
            <div class="flex flex-wrap gap-2">
                @for (place of markedPlaces(); track $index) {
                    <div class="group relative">
                        <div
                            class="flex cursor-pointer items-center gap-2 rounded-full border border-background-color-300 bg-background-color-100 px-3 py-1.5 shadow-shadow-200 transition-all duration-200 hover:border-main-color-600 hover:shadow-shadow-300"
                        >
                            <div
                                class="h-3 w-3 rounded-full border border-background-color-300 shadow-sm"
                                [style.backgroundColor]="place.color"
                            ></div>
                            <span
                                class="text-sm font-medium text-text-color-100"
                                >{{ place.name }}</span
                            >
                        </div>
                    </div>
                }
            </div>
        } @else {
            <div class="flex items-center justify-center py-8">
                <div class="text-center">
                    <div
                        class="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-background-color-200"
                    >
                        <i
                            class="pi pi-map-marker text-xl text-text-color-300"
                        ></i>
                    </div>
                    <p class="text-sm text-text-color-300">
                        No marked places yet
                    </p>
                </div>
            </div>
        }
    </div>

    <!-- Map Section -->
    <div class="flex-1">
        <app-map class="size-full" [nodes]="markedPlacesMap()"></app-map>
    </div>
</div>
