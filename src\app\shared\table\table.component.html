<div class="h-full w-full rounded-lg shadow-xl">
  <p-table
    [columns]="cols"
    [value]="data"
    [paginator]="data.length >= 10"
    [rows]="20"
    [scrollable]="true"
    [scrollHeight]="'flex'"
    [rowsPerPageOptions]="[10, 20, 50, 100]"
    [resizableColumns]="true"
    [columnResizeMode]="'fit'"
    styleClass=" h-full p-datatable-sm"
    [globalFilterFields]="selectedColumns | pluck : 'key'"
    (selectionChange)="_multiSelectSignal.set($event)"
    [selection]="_multiSelectSignal()"
    #dt1
    >
    @if (filter) {
      <ng-template pTemplate="caption">
        <div class="flex justify-between">
          @if (filter) {
            <span class="p-input-icon-left ml-auto">
              <i class="pi pi-search"></i>
              <input
                pInputText
                type="text"
                #f
                (input)="dt1.filterGlobal(f.value, 'contains')"
                [placeholder]="'Search keyword' | translate"
                />
              </span>
            }
          </div>
        </ng-template>
      }
      <ng-template pTemplate="header" let-columns>
        <tr>
          @if (multiSelect) {
            <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
          }

          @for (col of selectedColumns; track col) {
            <th
              pResizableColumn
              [pSortableColumn]="col.key"
              class="min-w-20"
              >
              <div class="text-[#545AE8]">
                @if (col.sort) {
                  <p-sortIcon
                    class="text-[#545AE8]"
                    [field]="col.key"
                  ></p-sortIcon>
                }
                <span class="text-xl">
                  {{ col.header | translate }}
                </span>
              </div>
            </th>
          }
          @if (actions.length > 0) {
            <th
              style="width: 5rem !important"
              class="relative"
              >
              <div class="text-[#545AE8]">
                <span class="text-xl">
                  {{ "action" | translate }}
                </span>
              </div>
            </th>
          }
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-columns="columns">
        <tr>
          @if(multiSelect){
            <td>
              <p-tableCheckbox [value]="rowData" />
            </td>
          }
          @for (col of selectedColumns; track col) {
            <td>
              <ng-container
                [ngTemplateOutlet]="col.type | templateRef"
            [ngTemplateOutletContext]="{
              value: col.type != 'link' ? rowData[col.key] : rowData,
              action: col.action,
              key: col.key
            }"
                >
              </ng-container>
            </td>
          }
          @if (actions.length > 0) {
            <td
              style="width: 5rem !important"
              class="relative"
              >
              <button
                pButton
                class="p-button-text p-button-rounded p-button-secondary"
                type="button"
                (click)="op.toggle($event)"
                icon="pi pi-ellipsis-v"
              ></button>
              <p-overlayPanel #op>
                <ng-template pTemplate="content">
                  @for (item of actions; track item) {
                    <div class="m-1 my-2">
                      @if (item.hide ? item.hide(rowData) : true) {
                        <a
                          (click)="exac(item, rowData)"
                          type="button"
                          [pTooltip]="item.label"
                          tooltipPosition="left"
                          [ngClass]="['p-2 px-3 cursor-pointer', item.class || '']"
                          >
                          <span [class]="item.icon"> </span>
                          <span class="px-1">
                            {{ item.label | translate }}
                          </span>
                        </a>
                      }
                    </div>
                  }
                </ng-template>
              </p-overlayPanel>
              <!-- <div class="flex justify-center gap-2 flex-wrap">
              <span *ngFor="let item of actions">
                <button
                  *ngIf="item.hide ? item.hide(rowData) : true"
                  (click)="exac(item, rowData)"
                  pButton
                  pRipple
                  [icon]="item.icon"
                  type="button"
                  [pTooltip]="item.label"
                  tooltipPosition="left"
                                [disabled]="
                                    item.prodection
                                        ? item.prodection(rowData)
                                        : false
                                "
                                [ngClass]="[
                                    'p-button-rounded p-button-text',
                                    item.class
                                ]"
                ></button>
              </span>
            </div> -->
          </td>
        }
      </tr>
    </ng-template>
  </p-table>
</div>

<ng-template templateRef="default" #default let-value="value">
  {{ value }}</ng-template
  >
  <ng-template templateRef="check" #check let-value="value">
    <input type="checkbox" class="size-6" disabled [checked]="value" />
  </ng-template>
  <ng-template templateRef="timeStamp" let-value="value" let-key="key">
    {{ value | date }}
  </ng-template>
