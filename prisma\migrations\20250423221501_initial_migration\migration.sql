-- Create<PERSON><PERSON>
CREATE TYPE "DriverStatus" AS ENUM ('NONE', 'IN_REVIEW', 'PENDING_ONSITE_REVIEW', 'REJECTED', 'APPROVED');

-- CreateEnum
CREATE TYPE "OrderStatus" AS ENUM ('PENDING', 'SUGGESTED_FOR_DRIVER', 'CONFIRMED', 'COMPLETED');

-- CreateEnum
CREATE TYPE "TripStatus" AS ENUM ('DRIVER_DIDNT_ARRIVE', 'DRIVER_WAITING_CLIENT', 'DRIVER_WITH_CLIENT', 'FINISHED');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "isPhoneVerified" BOOLEAN NOT NULL DEFAULT false,
    "verificationCode" TEXT,
    "driverStatus" "DriverStatus" NOT NULL DEFAULT 'NONE',

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StopPoint" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "StopPoint_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarkedPlace" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "MarkedPlace_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Point" (
    "id" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "Point_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "OrderStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "pickupStopPointId" TEXT,
    "pickupMarkedPlaceId" TEXT,
    "pickupPointId" TEXT,
    "dropoffStopPointId" TEXT,
    "dropoffMarkedPlaceId" TEXT,
    "dropoffPointId" TEXT,
    "tripId" TEXT,

    CONSTRAINT "Order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Trip" (
    "id" TEXT NOT NULL,
    "driverId" TEXT NOT NULL,
    "status" "TripStatus" NOT NULL DEFAULT 'DRIVER_DIDNT_ARRIVE',
    "currentLocationLatitude" DOUBLE PRECISION,
    "currentLocationLongitude" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Trip_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_phoneNumber_key" ON "User"("phoneNumber");

-- CreateIndex
CREATE INDEX "User_phoneNumber_idx" ON "User"("phoneNumber");

-- CreateIndex
CREATE INDEX "MarkedPlace_userId_idx" ON "MarkedPlace"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Order_tripId_key" ON "Order"("tripId");

-- CreateIndex
CREATE INDEX "Order_userId_idx" ON "Order"("userId");

-- CreateIndex
CREATE INDEX "Order_pickupStopPointId_idx" ON "Order"("pickupStopPointId");

-- CreateIndex
CREATE INDEX "Order_pickupMarkedPlaceId_idx" ON "Order"("pickupMarkedPlaceId");

-- CreateIndex
CREATE INDEX "Order_pickupPointId_idx" ON "Order"("pickupPointId");

-- CreateIndex
CREATE INDEX "Order_dropoffStopPointId_idx" ON "Order"("dropoffStopPointId");

-- CreateIndex
CREATE INDEX "Order_dropoffMarkedPlaceId_idx" ON "Order"("dropoffMarkedPlaceId");

-- CreateIndex
CREATE INDEX "Order_dropoffPointId_idx" ON "Order"("dropoffPointId");

-- CreateIndex
CREATE INDEX "Order_tripId_idx" ON "Order"("tripId");

-- CreateIndex
CREATE INDEX "Trip_driverId_idx" ON "Trip"("driverId");

-- AddForeignKey
ALTER TABLE "MarkedPlace" ADD CONSTRAINT "MarkedPlace_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_pickupStopPointId_fkey" FOREIGN KEY ("pickupStopPointId") REFERENCES "StopPoint"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_pickupMarkedPlaceId_fkey" FOREIGN KEY ("pickupMarkedPlaceId") REFERENCES "MarkedPlace"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_pickupPointId_fkey" FOREIGN KEY ("pickupPointId") REFERENCES "Point"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_dropoffStopPointId_fkey" FOREIGN KEY ("dropoffStopPointId") REFERENCES "StopPoint"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_dropoffMarkedPlaceId_fkey" FOREIGN KEY ("dropoffMarkedPlaceId") REFERENCES "MarkedPlace"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_dropoffPointId_fkey" FOREIGN KEY ("dropoffPointId") REFERENCES "Point"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_tripId_fkey" FOREIGN KEY ("tripId") REFERENCES "Trip"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Trip" ADD CONSTRAINT "Trip_driverId_fkey" FOREIGN KEY ("driverId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
