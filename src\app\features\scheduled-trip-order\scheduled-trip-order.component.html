<div
    class="pointer-events-auto m-0 flex h-full max-h-[95dvh] flex-col overflow-auto bg-background-color-100 p-0"
>
    <div
        class="flex h-full min-h-0 flex-1 flex-col overflow-auto bg-background-color-100"
    >
        <!-- Step 2: Select Pickup Location -->
        @if (currentStep() === OrderStep.SELECT_PICKUP) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 text-background-color-100"
                    >
                        <i class="pi pi-map-marker text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Select A Stop Point
                        </h3>
                    </div>
                </div>

                <!-- Marked Places Horizontal Scroll -->
                @if (markedPlaces().length > 0) {
                    <div class="border-b border-background-color-300 p-4">
                        <div class="flex gap-3 overflow-x-auto pb-2">
                            @for (place of markedPlaces(); track place.id) {
                                <div
                                    class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-background-color-100"
                                    (click)="selectMarkedPlace(place)"
                                >
                                    <span>{{ place.name }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <div class="flex h-auto min-h-0 flex-1 flex-col">
                    <app-location-picker
                        title="Pickup"
                        placeholder="Enter pickup location"
                        (locationSelected)="onPickupSelected($event)"
                    >
                    </app-location-picker>
                </div>
            </div>
        }
    </div>
</div>
