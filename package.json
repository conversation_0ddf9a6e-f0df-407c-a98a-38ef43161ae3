{"name": "user", "version": "19.0.1", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --disable-host-check", "build": "ng build", "watch": "ng build --watch --configuration development", "format": "prettier --write \"**/*.{js,mjs,ts,mts,d.ts,html}\" --cache", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.5", "@angular/common": "^19.2.5", "@angular/compiler": "^19.2.5", "@angular/core": "^19.2.5", "@angular/forms": "^19.2.5", "@angular/platform-browser": "^19.2.5", "@angular/platform-browser-dynamic": "^19.2.5", "@angular/router": "^19.2.5", "@angular/service-worker": "^19.2.5", "@bluehalo/ngx-leaflet": "^19.0.0", "@bluehalo/ngx-leaflet-draw": "^19.0.0", "@capacitor/angular": "2.0.3", "@capacitor/core": "latest", "@capacitor/geolocation": "^7.1.3", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeng/themes": "^19.0.5", "chart.js": "4.4.2", "date-fns": "^4.1.0", "firebase": "^11.7.3", "leaflet": "^1.9.4", "leaflet-draw": "1.0.2", "ngx-pipes": "^3.2.2", "ngx-spinner": "^19.0.0", "primeclt": "^0.1.5", "primeicons": "^7.0.0", "primeng": "^19.0.8", "rxjs": "~7.8.0", "tailwindcss-primeui": "^0.5.1", "tslib": "^2.3.0", "user": "file:", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.6", "@angular/cli": "^19.2.6", "@angular/compiler-cli": "^19.2.5", "@capacitor/cli": "latest", "@types/jasmine": "~5.1.0", "@types/leaflet": "^1.9.17", "@types/leaflet-draw": "^1.0.11", "autoprefixer": "^10.4.21", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "~5.6.2"}}