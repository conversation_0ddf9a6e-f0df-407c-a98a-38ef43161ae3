# Testing Guide

This guide explains how to run tests locally for the Tripos Backend project.

## Prerequisites

1. Make sure you have all dependencies installed:
```bash
npm install
```

2. Ensure you have Docker running (required for e2e tests)

3. Copy the test environment file:
```bash
cp .env.test .env.test.local
```

## Running Tests

### Unit Tests

Run all unit tests:
```bash
npm run test
```

Run unit tests in watch mode (useful during development):
```bash
npm run test:watch
```

Generate test coverage report:
```bash
npm run test:cov
```

### E2E Tests

1. First, ensure the test database is running:
```bash
# Start a fresh database instance
NODE_ENV=test npm run db:up

# Wait for database to be ready
sleep 5
```

2. Run the migrations for test database:
```bash
NODE_ENV=test npm run prisma:migrate
```

3. Run the e2e tests:
```bash
NODE_ENV=test npm run test:e2e
```

### Running Specific Tests

Run a specific test file:
```bash
npm run test src/user/user.service.spec.ts
```

Run tests matching a specific pattern:
```bash
npm run test -t "UserService"
```

## Test Environment

The test environment uses:
- Separate test database (defined in `.env.test`)
- In-memory JWT secret
- Shorter token expiration times

## Debugging Tests

1. Run tests in debug mode:
```bash
npm run test:debug
```

2. Use the VS Code debugger with the following configuration:
```json
{
  "type": "node",
  "request": "attach",
  "name": "Debug Tests",
  "port": 9229
}
```

## Common Issues

1. **Database Connection Errors**
   ```bash
   # Reset test database
   NODE_ENV=test npm run db:down
   NODE_ENV=test npm run db:up
   sleep 5
   NODE_ENV=test npm run prisma:migrate
   ```

2. **Prisma Client Issues**
   ```bash
   # Regenerate Prisma Client
   npm run prisma:generate
   ```

3. **Test Timeouts**
   - Increase timeout in `jest.config.js`
   - Default is 5000ms (5 seconds)

## Best Practices

1. **Test File Naming**
   - Unit tests: `*.spec.ts`
   - E2E tests: `*.e2e-spec.ts`

2. **Test Structure**
   ```typescript
   describe('ServiceName', () => {
     describe('methodName', () => {
       it('should do something', () => {
         // test code
       });
     });
   });
   ```

3. **Mocking**
   - Use `jest.mock()` for external dependencies
   - Use `jest.spyOn()` for internal methods
   - Keep mocks in `__mocks__` directory

4. **Database Testing**
   - Always use transactions in tests
   - Clean up after each test
   - Use factory functions for test data

## CI/CD Integration

Tests are automatically run in the CI pipeline:
- Unit tests run on every push
- E2E tests run on PR and main branch
- Coverage reports are generated and uploaded

For more details, see `.github/workflows/test.yml`