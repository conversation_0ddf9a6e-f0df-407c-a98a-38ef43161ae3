import {
    <PERSON>tt<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    HttpInterceptor,
    HttpRequest,
} from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, Observable, switchMap, tap, throwError } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    authService = inject(AuthService);
    router = inject(Router);

    intercept(
        request: HttpRequest<any>,
        next: HttpHand<PERSON>,
    ): Observable<HttpEvent<any>> {
        const token = localStorage.getItem('Access-token');
        request = request.clone({
            headers: request.headers.set('Authorization', `Bearer ${token}`),
        });
        return next.handle(request).pipe(
            catchError((err) => {
                if (
                    err.status == 401 &&
                    !request.url.includes('api/users/auth/refresh')
                ) {
                    return this.authService.refreshToken().pipe(
                        tap((val) => {
                            console.log(val);
                        }),
                        switchMap(
                            (tokens: {
                                data: {
                                    access_token: string;
                                    refresh_token: string;
                                };
                            }) => {
                                localStorage.setItem(
                                    'Access-token',
                                    tokens.data.access_token,
                                );
                                localStorage.setItem(
                                    'Refresh-token',
                                    tokens.data.refresh_token,
                                );
                                request = request.clone({
                                    setHeaders: {
                                        Authorization: `Bearer ${tokens.data.access_token}`,
                                    },
                                });
                                return next.handle(request);
                            },
                        ),
                        catchError((refreshErr) => {
                            // If refresh token fails, redirect to login
                            this.router.navigate(['/auth/login']);
                            return throwError(() => new Error('Authentication failed'));
                        }),
                    );
                }
                // For non-401 errors, pass through the original error
                return throwError(() => err);
            }),
        );
    }
}
