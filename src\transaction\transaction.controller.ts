import { Controller, Get, Post, UseGuards, Param } from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { AuthGuard, AdminGuard } from '../common/guards';
import { GetUser } from '../common/decorators';
import { MoneyTransaction, User } from '@prisma/client';

@Controller('api/transactions')
@UseGuards(AuthGuard)
export class TransactionController {
  constructor(private transactionService: TransactionService) {}

  @Get('me')
  async getMyTransactions(@GetUser() user: User): Promise<MoneyTransaction[]> {
    return this.transactionService.getUserTransactions(user.id);
  }

  @Get('driver/:driverId')
  @UseGuards(AdminGuard)
  async getDriverTransactions(@Param('driverId') driverId: string): Promise<MoneyTransaction[]> {
    return this.transactionService.getDriverTransactions(driverId);
  }

  @Post('driver/:driverId/mark-completed')
  @UseGuards(AdminGuard)
  async markDriverTransactionsAsCompleted(@Param('driverId') driverId: string): Promise<{ updatedCount: number }> {
    return this.transactionService.markDriverToCompanyTransactionsAsCompleted(driverId);
  }
}
