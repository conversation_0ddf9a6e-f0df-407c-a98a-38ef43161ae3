import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { DividerModule } from 'primeng/divider';
import { TagModule } from 'primeng/tag';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { User, DriverStatus, DriverFiles } from '../../core/types/tripoos.types';
import { UserService } from '../../services/user.service';
import { DriverService } from '../../services/driver.service';
import { AuthService } from '../../services/auth.service';
import { injectMany } from '../../shared/helpers/injectMany';

type SingleFileWithPreview = {
    file: File | null;
    previewUrl: string | null;
};

type FileCollection = {
    idFront: SingleFileWithPreview;
    idBack: SingleFileWithPreview;
    personalPhoto: SingleFileWithPreview;
    carPhotos: File[];
};

@Component({
    selector: 'app-user-profile',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        CardModule,
        ButtonModule,
        InputTextModule,
        DropdownModule,
        DividerModule,
        TagModule,
        ToastModule,
    ],
    templateUrl: './user-profile.component.html',
    styles: `
        :host {
            display: block;
            min-height: 100vh;
            background: #ffffff;
            color: #000000;
        }

        .profile-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            padding-left: 6rem !important; /* Force space for hamburger button */
        }

        @media (max-width: 768px) {
            .profile-container {
                padding: 1rem;
                padding-left: 5rem !important; /* Force space for hamburger button on mobile */
            }
        }

        .profile-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            border-bottom: 2px solid #f0f0f0;
        }

        .profile-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #000000;
            margin-bottom: 0.5rem;
        }

        .profile-subtitle {
            font-size: 1.1rem;
            color: #666666;
            font-weight: 400;
        }

        .info-card {
            background: #ffffff;
            border: 2px solid #000000;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #000000;
            font-size: 1rem;
        }

        .info-value {
            color: #333333;
            font-size: 1rem;
        }

        .status-tag {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-none {
            background: #f0f0f0;
            color: #666666;
        }

        .status-in-review {
            background: #008C45;
            color: #ffffff;
        }

        .status-approved {
            background: #008C45;
            color: #ffffff;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .driver-form {
            background: #fafafa;
            border: 2px solid #000000;
            border-radius: 8px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .form-field {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #000000;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #000000;
        }

        .action-button {
            background: #000000;
            color: #ffffff;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .action-button:hover {
            background: #333333;
        }

        .action-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        .file-upload {
            border: 2px dashed #d0d0d0;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .file-upload:hover {
            border-color: #000000;
        }

        .file-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 4px;
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .profile-container {
                padding: 1rem;
            }

            .profile-title {
                font-size: 2rem;
            }

            .info-card {
                padding: 1.5rem;
            }
        }
    `,
    providers: [MessageService],
})
export class UserProfileComponent implements OnInit {
    services = injectMany({
        UserService,
        DriverService,
        AuthService,
        FormBuilder,
        MessageService,
    });

    user = signal<User | null>(null);
    isLoading = signal<boolean>(false);
    driverForm: FormGroup;
    showDriverForm = signal<boolean>(false);
    isSubmitting = signal<boolean>(false);
    currentYear = new Date().getFullYear();
    yearOptions: number[] = [];
    URL = URL; // Make URL available in template

    files: FileCollection = {
        idFront: { file: null, previewUrl: null },
        idBack: { file: null, previewUrl: null },
        personalPhoto: { file: null, previewUrl: null },
        carPhotos: [],
    };

    constructor() {
        // Generate year options
        for (let year = 1990; year <= this.currentYear; year++) {
            this.yearOptions.push(year);
        }

        // Initialize driver form
        this.driverForm = this.services.FormBuilder.group({
            make: ['', [Validators.required]],
            model: ['', [Validators.required]],
            year: [this.currentYear, [Validators.required]],
            color: ['', [Validators.required]],
            licensePlate: ['', [Validators.required]],
        });
    }

    ngOnInit(): void {
        // First check if user data is already available in AuthService
        const authUser = this.services.AuthService.user();
        if (authUser) {
            this.user.set(authUser);
        }
        
        // Then load fresh user profile data
        this.loadUserProfile();
    }

    loadUserProfile(): void {
        this.isLoading.set(true);
        this.services.UserService.getProfile().subscribe({
            next: (response) => {
                if (response.data) {
                    this.user.set(response.data);
                    this.services.AuthService.user.set(response.data);
                } else {
                    console.warn('No user data in response');
                }
                this.isLoading.set(false);
            },
            error: (error) => {
                console.error('Error loading user profile:', error);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load user profile'
                });
                this.isLoading.set(false);
            }
        });
    }

    getDriverStatusText(status: DriverStatus): string {
        switch (status) {
            case DriverStatus.NONE:
                return 'Not Applied';
            case DriverStatus.IN_REVIEW:
                return 'Under Review';
            case DriverStatus.PENDING_ONSITE_REVIEW:
                return 'Pending Onsite Review';
            case DriverStatus.APPROVED:
                return 'Approved';
            case DriverStatus.REJECTED:
                return 'Rejected';
            default:
                return 'Unknown';
        }
    }

    getDriverStatusClass(status: DriverStatus): string {
        switch (status) {
            case DriverStatus.NONE:
                return 'status-none';
            case DriverStatus.IN_REVIEW:
            case DriverStatus.PENDING_ONSITE_REVIEW:
                return 'status-in-review';
            case DriverStatus.APPROVED:
                return 'status-approved';
            case DriverStatus.REJECTED:
                return 'status-rejected';
            default:
                return 'status-none';
        }
    }

    toggleDriverForm(): void {
        this.showDriverForm.set(!this.showDriverForm());
    }

    getUserFullName(): string {
        const user = this.user();
        if (!user) return '';
        
        // Try camelCase first (firstName, lastName)
        if (user.firstName || user.lastName) {
            return `${user.firstName || ''} ${user.lastName || ''}`.trim();
        }
        
        // Try snake_case as fallback (first_name, last_name)
        const userAny = user as any;
        if (userAny.first_name || userAny.last_name) {
            return `${userAny.first_name || ''} ${userAny.last_name || ''}`.trim();
        }
        
        return '';
    }

    onFileSelect(event: Event, field: keyof FileCollection): void {
        const input = event.target as HTMLInputElement;
        const files = input.files;

        if (files?.length && field !== 'carPhotos') {
            const singleField = field as Exclude<keyof FileCollection, 'carPhotos'>;
            if (this.files[singleField].previewUrl) {
                URL.revokeObjectURL(this.files[singleField].previewUrl!);
            }

            this.files[singleField] = {
                file: files[0],
                previewUrl: URL.createObjectURL(files[0]),
            };
        }
    }

    onCarPhotosSelect(event: Event): void {
        const input = event.target as HTMLInputElement;
        if (!input.files || this.files.carPhotos.length >= 5) return;

        const newFiles = Array.from(input.files);
        const remainingSlots = 5 - this.files.carPhotos.length;
        const filesToAdd = newFiles.slice(0, remainingSlots);

        this.files.carPhotos = [...this.files.carPhotos, ...filesToAdd];
    }

    removeCarPhoto(index: number): void {
        this.files.carPhotos.splice(index, 1);
    }

    removeFile(field: Exclude<keyof FileCollection, 'carPhotos'>): void {
        if (this.files[field].previewUrl) {
            URL.revokeObjectURL(this.files[field].previewUrl!);
        }
        this.files[field] = { file: null, previewUrl: null };
    }

    onSubmitDriverApplication(): void {
        if (this.driverForm.invalid) {
            this.services.MessageService.add({
                severity: 'error',
                summary: 'Form Error',
                detail: 'Please fill all required fields correctly'
            });
            return;
        }

        if (
            !this.files.idFront.file ||
            !this.files.idBack.file ||
            !this.files.personalPhoto.file ||
            this.files.carPhotos.length === 0
        ) {
            this.services.MessageService.add({
                severity: 'error',
                summary: 'Missing Files',
                detail: 'Please upload all required documents'
            });
            return;
        }

        this.isSubmitting.set(true);

        const driverData = this.driverForm.value;
        const files: DriverFiles = {
            idFront: this.files.idFront.file ? [this.files.idFront.file] : [],
            idBack: this.files.idBack.file ? [this.files.idBack.file] : [],
            personalPhoto: this.files.personalPhoto.file ? [this.files.personalPhoto.file] : [],
            carPhotos: this.files.carPhotos,
        };

        this.services.DriverService.becomeDriver(driverData, files).subscribe({
            next: (response) => {
                if (response.data) {
                    this.services.MessageService.add({
                        severity: 'success',
                        summary: 'Success',
                        detail: 'Driver application submitted successfully'
                    });
                    this.loadUserProfile(); // Refresh user data
                    this.showDriverForm.set(false);
                } else {
                    this.services.MessageService.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to submit driver application'
                    });
                }
                this.isSubmitting.set(false);
            },
            error: (error) => {
                console.error('Error submitting driver application:', error);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to submit driver application'
                });
                this.isSubmitting.set(false);
            }
        });
    }
} 