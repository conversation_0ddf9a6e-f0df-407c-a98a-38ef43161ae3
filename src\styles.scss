/* You can add global styles to this file, and also import other style files */

@use "./tailwind.css";
@use "../public/layout/layout.scss";
@use "primeicons/primeicons.css";
@use "leaflet/dist/leaflet.css";
@use "leaflet-draw/dist/leaflet.draw.css" as draw;
@use "./scrollbar.scss";
@use "./overrides.scss";

/* Hide Leaflet layers control */
.leaflet-control-layers,
.leaflet-control-layers-toggle,
.leaflet-control-layers-list {
    display: none !important;
}

:root {
    /* main colors*/
    --main-color-100: #ffffff;
    --main-color-200: #eaecf0;
    --main-color-300: #d0d5dd;
    --main-color-400: #cbd0d8;
    --main-color-500: #98a2b3;
    --main-color-600: #d9d9d9;
    --main-color-700: #b7b7b7;
    --main-color-800: #344054;
    --main-color-900: #d9e9ff;
    --main-color-950: #f1f1f1;
    --main-color-1000: #fdfdfd;

    /* primary brand color */
    --primary-brand-color-100: #59acf9;
    --primary-brand-color-200: #0070ff;
    --primary-brand-color-300: #130856;
    --primary-brand-color-400: rgba(0, 112, 255, 0.08);

    /* secondary brand color */
    --secondary-brand-color-100: #ff00dd;
    --secondary-brand-color-200: #9f10e9;
    --secondary-brand-color-300: rgba(159, 16, 233, 0.08);
    --secondary-brand-color-400: rgba(255, 0, 221, 0.08);
    --secondary-brand-color-500: #ae00f1;

    /* linear gradient */
    --linear-gradient: 90deg, #0070ff 0%, #902ee0 100%;

    /* green colors */
    --green-color-100: #66c888;
    --green-color-200: #17b26a;
    --green-color-300: #66c88814;

    /* orange colors */
    --orange-color-100: #f5df12;
    --orange-color-200: #e37055;
    --orange-color-300: #f59312;
    --orange-color-400: #fef5ea;
    --orange-color-500: rgba(245, 147, 18, 0.08);
    --orange-color-600: rgba(242, 215, 0, 0.08);
    --orange-color-700: #d96832;

    /* red colors */
    --red-color-100: #fda29b;
    --red-color-200: #d92d20;
    --red-color-300: #ffeeec;
    --red-color-400: #d92d2014;
    --red-color-500: #ffeae9;

    /* pink colors */
    --pink-color-100: #f8ecfe;

    /* shadows */
    --main-shadow-100: 1px 0px 9px 0px rgba(0, 0, 0, 0.09);
    --main-shadow-200:
        0px -1px 0px 0px #ccc inset, 1px 0px 0px 0px #ebebeb inset,
        -1px 0px 0px 0px #ebebeb inset, 0px 0px 0px 0px rgba(0, 0, 0, 0.4);
    --main-shadow-600:
        0px 12px 16px -4px rgba(36, 36, 36, 0.08),
        0px 4px 6px -2px rgba(36, 36, 36, 0.03);
    --main-shadow-700: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

    --main-shadow-300:
        1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset,
        -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset,
        0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset,
        0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset,
        0px 4px 6px -2px rgba(26, 26, 26, 0.2);
    --main-shadow-400:
        0px 12px 16px -4px rgba(255, 255, 255, 0.08),
        0px 4px 6px -2px rgba(255, 255, 255, 0.03);
    --main-shadow-500: 0px 4px 4px 0px rgba(0, 0, 0, 0.18);

    --border-color-400: #e0e0e0;
}

:root[data-theme="light"] {
    /* background colors */
    --p-datepicker-panel-background: #ffffff;
    --p-dialog-background: #ffffff;
    --background-color-100: #ffffff;
    --background-color-200: #000000;
    --background-color-300: #bfbfbf;
    --background-color-400: #fbfbfb;
    --background-color-500: #ffffff;
    --background-color-600: #cbd0d8;
    --background-color-650: #d4e2fd;
    --background-color-700: #f6f9fe;
    --background-color-800: #f6f9fe;
    --background-color-900: #e8f1fd;
    --background-color-1000: #f3f4f6;
    --background-color-1100: #eaecf0;

    /* text colors */
    --text-color-100: #000000;
    --text-color-200: #ffffff;
    --text-color-300: #575757;
    --text-color-400: #303030;
    --text-color-500: #130856;

    /* border colors */
    --border-color-100: #000000;
    --border-color-200: #d0d5dd;
    --border-color-300: #d0d5dd;
    --border-color-400: #f0f0f0;

    /* shadow */
    --shadow-100: 0px 0px 13.333px 2.667px rgba(0, 0, 0, 0.08);
    --shadow-200:
        0px 12px 16px -4px rgba(36, 36, 36, 0.08),
        0px 4px 6px -2px rgba(36, 36, 36, 0.03);
    --shadow-300: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

:root[data-theme="dark"] {
    /* background colors */
    --background-color-100: #000000;
    --background-color-200: #ffffff;
    --background-color-300: #2c3541;
    --background-color-400: #1b1b1b;
    --background-color-500: #3f4a5c;
    --background-color-600: #344054;
    --background-color-650: #3f4a5c;
    --background-color-700: #3f4a5c;
    --background-color-800: #4b5464;
    --background-color-900: #e8f1fd;
    --background-color-1000: #3f4a5c;
    --background-color-1100: #98a2b3;

    /* text colors */
    --text-color-100: #ffffff;
    --text-color-200: #000000;
    --text-color-300: #969696;
    --text-color-400: #ffffff;
    --text-color-500: #130856;

    /* border colors */
    --border-color-100: #ffffff;
    --border-color-200: #98a2b3;
    --border-color-300: #3f4a5c;
    --border-color-400: #444b59;

    /* shadow */
    --shadow-100: 0px 0px 13.333px 2.667px rgba(255, 255, 255, 0.08);
    --shadow-200:
        0px 12px 16px -4px rgba(255, 255, 255, 0.08),
        0px 4px 6px -2px rgba(255, 255, 255, 0.03);
    --shadow-300:
        0px 12px 16px -4px rgba(255, 255, 255, 0.08),
        0px 4px 6px -2px rgba(255, 255, 255, 0.03);
}
