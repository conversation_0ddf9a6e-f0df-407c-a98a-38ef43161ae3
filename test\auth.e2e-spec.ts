import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/common/prisma/prisma.service';

describe('Authentication (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeAll(async () => {
    try {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      app = moduleFixture.createNestApplication();
      app.useGlobalPipes(new ValidationPipe());
      
      prisma = app.get<PrismaService>(PrismaService);
      
      await app.init();
    } catch (error) {
      console.error('Error during setup:', error);
      throw error;
    }
  });

  beforeEach(async () => {
    try {
      // Clean up ALL related tables in the correct order
      await prisma.$transaction([
        prisma.order.deleteMany(),
        prisma.trip.deleteMany(),
        prisma.markedPlace.deleteMany(),
        prisma.stopPoint.deleteMany(),
        prisma.refreshToken.deleteMany(),
        prisma.user.deleteMany(),
      ]);

      // Add delay to ensure DB is clean
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error in beforeEach:', error);
      throw error;
    }
  });

  afterAll(async () => {
    try {
      if (prisma) {
        await prisma.$disconnect();
      }
      if (app) {
        await app.close();
      }
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  });

  describe('POST /api/users/auth/sign-up', () => {
    const signUpDto = {
      first_name: 'John',
      last_name: 'Doe',
      phone_number: '+1234567890',
      password: 'password123'
    };

    beforeEach(async () => {
      await prisma.user.deleteMany();
    });

    it('should create a new user and return tokens', () => {
      return request(app.getHttpServer())
        .post('/api/users/auth/sign-up')
        .send(signUpDto)
        .expect(201)
        .expect(({ body }) => {
          expect(body.verificationCode).toBeDefined();
        });
    });

    it('should fail if phone number already exists for verified user', async () => {
      // Create and verify a user first
      await prisma.user.create({
        data: {
          firstName: signUpDto.first_name,
          lastName: signUpDto.last_name,
          phoneNumber: signUpDto.phone_number,
          password: 'hashedpassword',
          isPhoneVerified: true,
        },
      });

      // Try to signup with same phone number
      return request(app.getHttpServer())
        .post('/api/users/auth/sign-up')
        .send(signUpDto)
        .expect(400)
        .expect(({ body }) => {
          expect(body.message).toContain('phone number already exists');
        });
    });

    it('should update existing unverified user', async () => {
      // Create unverified user first
      const oldUser = await prisma.user.create({
        data: {
          firstName: 'Old',
          lastName: 'Name',
          phoneNumber: signUpDto.phone_number,
          password: 'oldpassword',
          isPhoneVerified: false,
        },
      });

      // Try to signup with same phone number
      return request(app.getHttpServer())
        .post('/api/users/auth/sign-up')
        .send(signUpDto)
        .expect(201)
        .expect(async ({ body }) => {
          expect(body.verificationCode).toBeDefined();
          
          // Verify the user was updated
          const updatedUser = await prisma.user.findUnique({
            where: { phoneNumber: signUpDto.phone_number },
          });
          expect(updatedUser.firstName).toBe(signUpDto.first_name);
          expect(updatedUser.lastName).toBe(signUpDto.last_name);
          expect(updatedUser.isPhoneVerified).toBe(false);
        });
    });
  });

  describe('POST /api/users/auth/sign-in', () => {
    const user = {
      first_name: 'John',
      last_name: 'Doe',
      phone_number: '+1234567890',
      password: 'password123'
    };

    beforeEach(async () => {
      // First create and verify a new user
      const signUpResponse = await request(app.getHttpServer())
        .post('/api/users/auth/sign-up')
        .send(user);

      // Get the verification code from the response
      const verificationCode = signUpResponse.body.verificationCode;

      // Verify the phone number using the verification endpoint
      await request(app.getHttpServer())
        .post('/api/users/auth/verify-phone')
        .send({
          phone_number: user.phone_number,
          code: verificationCode
        })
        .expect(201);
    });

    it('should sign in successfully with correct credentials', () => {
      return request(app.getHttpServer())
        .post('/api/users/auth/sign-in')
        .send({
          phone_number: user.phone_number,
          password: user.password
        })
        .expect(201)
        .expect(({ body }) => {
          expect(body.access_token).toBeDefined();
          expect(body.refresh_token).toBeDefined();
        });
    });

    it('should fail with wrong password', () => {
      return request(app.getHttpServer())
        .post('/api/users/auth/sign-in')
        .send({
          phone_number: user.phone_number,
          password: 'wrongpassword'
        })
        .expect(400)
        .expect(({ body }) => {
          expect(body.message).toContain('Invalid');
        });
    });

    it('should fail with non-existent phone number', () => {
      return request(app.getHttpServer())
        .post('/api/users/auth/sign-in')
        .send({
          phone_number: '+9999999999',
          password: user.password
        })
        .expect(400)
        .expect(({ body }) => {
          expect(body.message).toContain('Invalid');
        });
    });
  });
});
