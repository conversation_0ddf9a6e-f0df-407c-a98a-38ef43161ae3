import { Modu<PERSON> } from '@nestjs/common';
import { StopPointController } from './stop-point.controller';
import { StopPointService } from './stop-point.service';
import { JwtModule } from '@nestjs/jwt';
import { PrismaModule } from '../common/prisma/prisma.module';
import { jwtConfig } from '../common/config/jwt.config';

@Module({
  imports: [PrismaModule, JwtModule.registerAsync(jwtConfig)],
  controllers: [StopPointController],
  providers: [StopPointService],
})
export class StopPointModule {}
