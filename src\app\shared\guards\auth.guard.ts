import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { HttpService } from '../../services/http.service';

export const isAuthenticatedGuard = (): CanActivateFn => {
    return () => {
        const router = inject(Router);
        const http = inject(HttpService);
        const auth = inject(AuthService);

        if (localStorage.getItem('Access-token')) {
            http.get({
                link: `api/users/me`,
                failedToast: false,
                successToast: false,
                showloader: false,
            }).subscribe((val) => {
                auth.user.set(val.data);
            });
            return true;
        }

        return router.parseUrl('login');
    };
};
