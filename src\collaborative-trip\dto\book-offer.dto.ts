import { IsNumber, IsInt, IsPositive, Min, Max, IsString, IsOptional } from 'class-validator';

export class BookOfferDto {
  @IsInt()
  @IsPositive()
  passengers: number;

  @IsNumber()
  @Min(-90)
  @Max(90)
  pickupLatitude: number;

  @IsNumber()
  @Min(-180)
  @Max(180)
  pickupLongitude: number;

  @IsNumber()
  @Min(-90)
  @Max(90)
  dropoffLatitude: number;

  @IsNumber()
  @Min(-180)
  @Max(180)
  dropoffLongitude: number;

  @IsString()
  @IsOptional()
  passengerNotes?: string;
} 