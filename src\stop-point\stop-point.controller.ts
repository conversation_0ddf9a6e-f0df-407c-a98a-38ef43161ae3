import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { StopPointService } from './stop-point.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { AdminGuard } from '../common/guards/admin.guard';
import { StopPoint } from '@prisma/client';
import { CreateStopPointDto } from './dto/create-stop-point.dto';
import { UpdateStopPointDto } from './dto/update-stop-point.dto';

@Controller('api/stop-points')
@UseGuards(AuthGuard)
export class StopPointController {
  constructor(private readonly stopPointService: StopPointService) {}

  @Get()
  findAll(): Promise<StopPoint[]> {
    return this.stopPointService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<StopPoint> {
    return this.stopPointService.findOne(id);
  }

  @Post()
  @UseGuards(AdminGuard)
  create(@Body() createStopPointDto: CreateStopPointDto): Promise<StopPoint> {
    return this.stopPointService.create(createStopPointDto);
  }

  @Patch(':id')
  @UseGuards(AdminGuard)
  update(
    @Param('id') id: string,
    @Body() updateStopPointDto: UpdateStopPointDto,
  ): Promise<StopPoint> {
    return this.stopPointService.update(id, updateStopPointDto);
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  delete(@Param('id') id: string): Promise<StopPoint> {
    return this.stopPointService.delete(id);
  }
}
