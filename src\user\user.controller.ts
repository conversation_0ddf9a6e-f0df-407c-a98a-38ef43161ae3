import { Body, Controller, Post, Get, UseGuards, Query, Param, Put } from '@nestjs/common';
import { UserService } from './user.service';
import {
  SignUpDto,
  SignInDto,
  RefreshTokenDto,
  VerifyPhoneDto,
  GetUsersDto,
  AnalyticsDto,
} from './dto';
import { GetUser } from 'src/common/decorators/get-user.decorator';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { AdminGuard } from 'src/common/guards/admin.guard';
import { User } from '@prisma/client';
import { UltraMsgService } from 'src/common/ultramsg/ultramsg.service';
import { log } from 'console';

@Controller('api/users')
export class UserController {
  constructor(
    private userService: UserService,
    private ultramsgService: UltraMsgService,
  ) {}

  @Post('auth/sign-up')
  signUp(@Body() body: SignUpDto) {
    return this.userService.signUp(body);
  }

  @Post('auth/sign-in')
  signIn(@Body() body: SignInDto) {
    return this.userService.logIn(body);
  }

  @Post('auth/verify-phone')
  verifyPhone(@Body() body: VerifyPhoneDto) {
    return this.userService.verifyPhone(body.phone_number, body.code);
  }

  @Post('auth/send-verification-code')
  sendVerificationCode(@Body('phone_number') phoneNumber: string) {
    return this.ultramsgService.sendVerificationCode(phoneNumber);
  }

  @Post('auth/refresh')
  refresh(@Body() body: RefreshTokenDto) {
    return this.userService.refresh(body);
  }

  @UseGuards(AuthGuard)
  @Get('me')
  getProfile(@GetUser('id') userId: string) {
    return this.userService.getUserById(userId);
  }

  @Get()
  @UseGuards(AuthGuard)
  async findAll(@Query() query: GetUsersDto) {
    return this.userService.getUsers(query.driverStatus);
  }

  // Admin endpoints
  @Get('admin/drivers')
  @UseGuards(AdminGuard)
  async getAllDrivers() {
    return this.userService.getAllDrivers();
  }

  @Get('admin/drivers/in-review')
  @UseGuards(AdminGuard)
  async getDriversInReview() {
    return this.userService.getDriversInReview();
  }

  @Get('admin/drivers/pending-onsite-review')
  @UseGuards(AdminGuard)
  async getDriversPendingOnsiteReview() {
    return this.userService.getDriversPendingOnsiteReview();
  }

  @Put('admin/drivers/:driverId/approve-to-onsite-review')
  @UseGuards(AdminGuard)
  async approveDriverToOnsiteReview(@Param('driverId') driverId: string) {
    return this.userService.approveDriverToOnsiteReview(driverId);
  }

  @Put('admin/drivers/:driverId/approve')
  @UseGuards(AdminGuard)
  async approveDriver(@Param('driverId') driverId: string) {
    return this.userService.approveDriver(driverId);
  }

  @Put('admin/drivers/:driverId/reject')
  @UseGuards(AdminGuard)
  async rejectDriver(@Param('driverId') driverId: string) {
    return this.userService.rejectDriver(driverId);
  }

  @Post('admin/analytics')
  @UseGuards(AdminGuard)
  async getAnalytics(@Body() dto: AnalyticsDto) {
    return this.userService.getAnalytics(dto);
  }
}
