<div class="flex flex-1 flex-col bg-background-color-100">
    <!-- Step Header -->
    <div
        class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
    >
        <div
            class="flex h-12 w-12 items-center justify-center rounded-full bg-main-color-600 text-white"
        >
            <i class="pi pi-map-marker text-lg"></i>
        </div>
        <div>
            <h2 class="text-2xl font-semibold text-text-color-100">
                Select Locations
            </h2>
            <p class="text-text-color-300">
                Choose your pickup and destination
            </p>
        </div>
    </div>

    <!-- Current Step Indicator -->
    @if (currentLocationStep() === "pickup") {
        <!-- Pickup Location Selection -->
        <div
            class="flex items-center gap-4 border-b border-background-color-300 p-6"
        >
            <div
                class="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 text-white"
            >
                <i class="pi pi-map-marker text-lg"></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-text-color-100">
                    Pick your pickup location
                </h3>
            </div>
        </div>

        <!-- Marked Places Horizontal Scroll for Pickup -->
        @if (markedPlaces().length > 0) {
            <div class="border-b border-background-color-300 p-4">
                <h4 class="mb-3 text-sm font-semibold text-text-color-100">
                    Quick Select from Saved Places
                </h4>
                <div class="flex gap-3 overflow-x-auto pb-2">
                    @for (place of markedPlaces(); track place.id) {
                        <div
                            class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-background-color-100"
                            (click)="selectMarkedPlace(place)"
                        >
                            <span>{{ place.name }}</span>
                        </div>
                    }
                </div>
            </div>
        }

        <div class="flex h-auto min-h-0 flex-1 flex-col">
            <app-location-picker
                title="Pickup"
                placeholder="Enter pickup location"
                (locationSelected)="onPickupSelected($event)"
            ></app-location-picker>
        </div>
    }

    @if (currentLocationStep() === "dropoff") {
        <!-- Pickup Location Display -->
        <div
            class="flex items-center gap-4 border-b border-background-color-300 p-6"
        >
            <div
                class="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 text-white"
            >
                <i class="pi pi-check text-lg"></i>
            </div>
            <div>
                <h4 class="mb-1 text-sm font-medium text-text-color-100">
                    Pickup
                </h4>
                <p class="truncate text-sm text-text-color-300">
                    {{ pickupAddress() || "Selected location" }}
                </p>
            </div>
        </div>

        <!-- Dropoff Location Selection -->
        <div
            class="flex items-center gap-4 border-b border-background-color-300 p-6"
        >
            <div
                class="flex h-12 w-12 items-center justify-center rounded-full bg-red-600 text-white"
            >
                <i class="pi pi-map-marker text-lg"></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-text-color-100">
                    Where to?
                </h3>
            </div>
        </div>

        <!-- Marked Places Horizontal Scroll for Dropoff -->
        @if (markedPlaces().length > 0) {
            <div class="border-b border-background-color-300 p-4">
                <h4 class="mb-3 text-sm font-semibold text-text-color-100">
                    Quick Select from Saved Places
                </h4>
                <div class="flex gap-3 overflow-x-auto pb-2">
                    @for (place of markedPlaces(); track place.id) {
                        <div
                            class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-background-color-100"
                            (click)="selectMarkedPlaceAsDropoff(place)"
                        >
                            <span>{{ place.name }}</span>
                        </div>
                    }
                </div>
            </div>
        }

        <div class="flex min-h-0 flex-1 flex-col">
            <app-location-picker
                title="Destination"
                placeholder="Enter destination"
                (locationSelected)="onDropoffSelected($event)"
            ></app-location-picker>
        </div>
    }

    @if (currentLocationStep() === "confirm") {
        <!-- Enhanced Route Confirmation with Map -->
        <div class="flex h-full flex-col bg-background-color-100">
            <!-- Header -->
            <div class="flex-shrink-0 p-6 pb-4">
                <div class="text-center">
                    <i
                        class="pi pi-map-marker mb-4 text-4xl text-main-color-600"
                    ></i>
                    <h2 class="mb-2 text-xl font-semibold text-text-color-100">
                        Confirm Your Route
                    </h2>
                    <p class="text-sm text-text-color-300">
                        Review your route and add waypoints if needed
                    </p>
                </div>
            </div>

            <!-- Route Summary -->
            @if (routeInfo() && !isCalculatingRoute()) {
                <div class="flex-shrink-0 px-6 pb-4">
                    <div class="rounded-lg bg-background-color-100 p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                @if (routeInfo()?.totalDistance) {
                                    <div class="text-center">
                                        <div
                                            class="text-lg font-semibold text-text-color-100"
                                        >
                                            {{
                                                routeInfo()!.totalDistance!.toFixed(
                                                    1
                                                )
                                            }}
                                            km
                                        </div>
                                        <div
                                            class="text-xs text-text-color-300"
                                        >
                                            Distance
                                        </div>
                                    </div>
                                }
                                @if (routeInfo()?.totalDuration) {
                                    <div class="text-center">
                                        <div
                                            class="text-lg font-semibold text-text-color-100"
                                        >
                                            {{
                                                routeInfo()!.totalDuration?.toFixed(
                                                    0
                                                )
                                            }}
                                            min
                                        </div>
                                        <div
                                            class="text-xs text-text-color-300"
                                        >
                                            Duration
                                        </div>
                                    </div>
                                }
                                <div class="text-center">
                                    <div
                                        class="text-lg font-semibold text-text-color-100"
                                    >
                                        {{ waypointCount() }}
                                    </div>
                                    <div class="text-xs text-text-color-300">
                                        Waypoints
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Map Container -->
            <div class="flex min-h-0 flex-1 flex-col px-6">
                <div class="relative flex-1 overflow-hidden rounded-lg">
                    @if (isCalculatingRoute()) {
                        <div
                            class="absolute inset-0 z-10 flex items-center justify-center bg-background-color-100 bg-opacity-75"
                        >
                            <div class="text-center">
                                <p-progressSpinner
                                    styleClass="w-8 h-8"
                                    strokeWidth="4"
                                    fill="transparent"
                                    animationDuration="1s"
                                >
                                </p-progressSpinner>
                                <p class="mt-2 text-sm text-text-color-300">
                                    Calculating route...
                                </p>
                            </div>
                        </div>
                    }
                    <div class="h-[80dvh]">
                        <app-map
                            [nodes]="mapLayers()"
                            (clickMap)="onMapClick($event)"
                            class="block h-full w-full"
                        ></app-map>
                    </div>
                </div>
            </div>

            <!-- Waypoints List -->
            @if (routeInfo() && routeInfo()!.waypoints.length > 2) {
                <div class="flex-shrink-0 px-6 pb-4">
                    <div class="rounded-lg bg-background-color-100 p-4">
                        <h3
                            class="mb-3 text-sm font-semibold text-text-color-100"
                        >
                            Waypoints
                        </h3>
                        <div class="space-y-2">
                            @for (
                                waypoint of IntermediateWaypoints();
                                track waypoint.id
                            ) {
                                <div
                                    class="flex items-center justify-between rounded-lg bg-background-color-100 p-3"
                                >
                                    <div class="flex items-center space-x-3">
                                        <div
                                            class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white"
                                        >
                                            {{ $index + 1 }}
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <p
                                                class="text-sm text-text-color-100"
                                            >
                                                {{
                                                    waypoint.address ||
                                                        "Waypoint " +
                                                            ($index + 1)
                                                }}
                                            </p>
                                            <p
                                                class="text-xs text-text-color-300"
                                            >
                                                {{
                                                    waypoint.location.lat.toFixed(
                                                        6
                                                    )
                                                }},
                                                {{
                                                    waypoint.location.lng.toFixed(
                                                        6
                                                    )
                                                }}
                                            </p>
                                        </div>
                                    </div>
                                    <button
                                        class="rounded-lg bg-red-500 p-2 text-white hover:bg-red-600"
                                        (click)="removeWaypoint(waypoint.id)"
                                        title="Remove waypoint"
                                    >
                                        <i class="pi pi-times text-xs"></i>
                                    </button>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Instructions -->
            <div class="flex-shrink-0 p-6 pt-4">
                <div class="rounded-lg bg-blue-50 p-3 text-sm text-blue-800">
                    <i class="pi pi-info-circle mr-2"></i>
                    Click on the map to add waypoints to your route
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex-shrink-0 p-6 pt-0">
                <div class="flex space-x-3">
                    <button
                        class="flex flex-1 items-center justify-center rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50"
                        (click)="goBack()"
                    >
                        <i class="pi pi-arrow-left mr-2"></i>
                        Back
                    </button>
                    <button
                        class="flex flex-1 items-center justify-center rounded-lg bg-background-color-200 px-4 py-3 text-sm font-medium text-white hover:bg-main-color-700"
                        (click)="confirmLocations()"
                        [disabled]="!canConfirm()"
                    >
                        <i class="pi pi-check mr-2"></i>
                        Continue to Time Selection
                    </button>
                </div>
            </div>
        </div>
    }
</div>
