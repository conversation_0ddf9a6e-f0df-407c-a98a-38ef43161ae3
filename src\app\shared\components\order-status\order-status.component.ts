import {
    Compo<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    inject,
    signal,
    input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { Tooltip } from 'primeng/tooltip';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { interval, Subscription, switchMap, startWith } from 'rxjs';
import {
    Order,
    OrderService,
    OrderStatus,
    Trip,
    TripStatus,
} from '../../../services/order.service';

@Component({
    selector: 'app-order-status',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        TagModule,
        ProgressSpinnerModule,
        ToastModule,
        Tooltip,
    ],
    template: `
        <div class="order-status-container">
            @if (order()) {
                <p-card>
                    <ng-template pTemplate="header">
                        <div class="bg-primary p-3 text-white">
                            <div
                                class="justify-content-between align-items-center flex"
                            >
                                <h3 class="m-0">
                                    <i class="pi pi-car mr-2"></i>
                                    Order #{{ order()!.id.substring(0, 8) }}
                                </h3>
                                <p-tag
                                    [value]="order()!.status"
                                    [severity]="
                                        getStatusSeverity(order()!.status)
                                    "
                                    [icon]="getStatusIcon(order()!.status)"
                                ></p-tag>
                            </div>
                        </div>
                    </ng-template>

                    <div class="order-details">
                        <!-- Order Information -->
                        <div class="mb-3">
                            <div class="align-items-center mb-2 flex">
                                <i
                                    class="pi pi-calendar mr-2 text-blue-500"
                                ></i>
                                <span class="font-semibold">Created:</span>
                                <span class="ml-2">{{
                                    formatDate(order()!.createdAt)
                                }}</span>
                            </div>

                            @if (order()!.pickupPointId) {
                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-map-marker mr-2 text-green-500"
                                    ></i>
                                    <span class="font-semibold"
                                        >Pickup Point:</span
                                    >
                                    <span class="ml-2">{{
                                        order()!.pickupPointId?.substring(0, 8)
                                    }}</span>
                                </div>
                            }

                            @if (order()!.dropoffPointId) {
                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-flag-fill mr-2 text-red-500"
                                    ></i>
                                    <span class="font-semibold"
                                        >Dropoff Point:</span
                                    >
                                    <span class="ml-2">{{
                                        order()!.dropoffPointId?.substring(0, 8)
                                    }}</span>
                                </div>
                            }

                            @if (order()!.lastSuggestedAt) {
                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-clock mr-2 text-orange-500"
                                    ></i>
                                    <span class="font-semibold"
                                        >Last Suggested:</span
                                    >
                                    <span class="ml-2">{{
                                        order()!.lastSuggestedAt
                                            ? formatDate(
                                                  order()!.lastSuggestedAt
                                              )
                                            : 'N/A'
                                    }}</span>
                                </div>
                            }

                            @if (order()!.lastSuggestedDriverId) {
                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-user mr-2 text-purple-500"
                                    ></i>
                                    <span class="font-semibold"
                                        >Suggested Driver:</span
                                    >
                                    <span class="ml-2">{{
                                        order()!.lastSuggestedDriverId?.substring(
                                            0,
                                            8
                                        )
                                    }}</span>
                                </div>
                            }
                        </div>

                        <!-- Trip Information (if order is confirmed) -->
                        @if (order()!.tripId && trip()) {
                            <div
                                class="trip-details border-top-1 surface-border pt-3"
                            >
                                <h4 class="mb-3">
                                    <i class="pi pi-car mr-2"></i>
                                    Trip Details
                                </h4>

                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-id-card mr-2 text-blue-500"
                                    ></i>
                                    <span class="font-semibold">Trip ID:</span>
                                    <span class="ml-2">{{
                                        trip()!.id.substring(0, 8)
                                    }}</span>
                                </div>

                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-user mr-2 text-green-500"
                                    ></i>
                                    <span class="font-semibold">Driver:</span>
                                    <span class="ml-2">{{
                                        trip()!.driverId.substring(0, 8)
                                    }}</span>
                                </div>

                                <div class="align-items-center mb-2 flex">
                                    <p-tag
                                        [value]="trip()!.status"
                                        [severity]="
                                            getTripStatusSeverity(
                                                trip()!.status
                                            )
                                        "
                                        [icon]="
                                            getTripStatusIcon(trip()!.status)
                                        "
                                    ></p-tag>
                                </div>

                                @if (
                                    trip()!.currentLocationLatitude &&
                                    trip()!.currentLocationLongitude
                                ) {
                                    <div class="align-items-center mb-2 flex">
                                        <i
                                            class="pi pi-map mr-2 text-orange-500"
                                        ></i>
                                        <span class="font-semibold"
                                            >Driver Location:</span
                                        >
                                        <span class="ml-2">
                                            {{
                                                trip()!.currentLocationLatitude!.toFixed(
                                                    6
                                                )
                                            }},
                                            {{
                                                trip()!.currentLocationLongitude!.toFixed(
                                                    6
                                                )
                                            }}
                                        </span>
                                    </div>
                                }
                            </div>
                        }

                        <!-- Status Messages -->
                        <div
                            class="status-message border-round mt-3 p-3"
                            [ngClass]="getStatusMessageClass(order()!.status)"
                        >
                            <div class="align-items-center flex">
                                @if (isLoading()) {
                                    <p-progressSpinner
                                        [style]="{
                                            width: '20px',
                                            height: '20px',
                                        }"
                                        styleClass="mr-2"
                                    ></p-progressSpinner>
                                }
                                <span>{{
                                    getStatusMessage(order()!.status)
                                }}</span>
                            </div>
                        </div>

                        <!-- Trip Details Button -->
                        @if (order()!.tripId && trip()) {
                            <div class="mt-3">
                                <button
                                    pButton
                                    label="View Full Trip Details"
                                    icon="pi pi-external-link"
                                    class="p-button-outlined w-full"
                                    (click)="viewTripDetails()"
                                ></button>
                            </div>
                        }

                        <!-- Auto-refresh indicator -->
                        <div
                            class="align-items-center justify-content-between text-500 mt-3 flex text-sm"
                        >
                            <div class="align-items-center flex gap-2">
                                <button
                                    pButton
                                    icon="pi pi-refresh"
                                    class="p-button-text p-button-sm"
                                    (click)="manualRefresh()"
                                    [pTooltip]="'Refresh now'"
                                ></button>
                            </div>
                        </div>
                    </div>
                </p-card>
            } @else {
                <div class="align-items-center justify-content-center flex p-4">
                    <p-progressSpinner></p-progressSpinner>
                    <span class="ml-2">Loading order details...</span>
                </div>
            }
        </div>
    `,
    styles: [
        `
            .order-status-container {
                max-width: 500px;
                margin: 0 auto;
            }

            .order-details {
                padding: 1rem;
            }

            .trip-details {
                background-color: var(--surface-50);
                padding: 1rem;
                border-radius: 6px;
            }

            .status-message {
                font-weight: 500;
            }

            .status-pending {
                background-color: var(--blue-50);
                color: var(--blue-700);
                border: 1px solid var(--blue-200);
            }

            .status-suggested {
                background-color: var(--yellow-50);
                color: var(--yellow-700);
                border: 1px solid var(--yellow-200);
            }

            .status-confirmed {
                background-color: var(--green-50);
                color: var(--green-700);
                border: 1px solid var(--green-200);
            }

            .status-completed {
                background-color: var(--purple-50);
                color: var(--purple-700);
                border: 1px solid var(--purple-200);
            }
        `,
    ],
})
export class OrderStatusComponent implements OnInit, OnDestroy {
    // Input properties (for standalone usage)
    orderId = input<string>();

    // Services
    private orderService = inject(OrderService);
    private messageService = inject(MessageService);
    private router = inject(Router);
    private config = inject(DynamicDialogConfig, { optional: true });

    // Get orderId from either input or dialog data
    private getOrderId(): string {
        return this.orderId() || this.config?.data?.orderId || '';
    }

    // Signals
    order = signal<Order | null>(null);
    trip = signal<Trip | null>(null);
    isLoading = signal<boolean>(false);
    lastUpdated = signal<Date>(new Date());
    previousOrderStatus = signal<OrderStatus | null>(null);

    // Subscriptions
    private refreshSubscription?: Subscription;

    ngOnInit(): void {
        this.startAutoRefresh();
    }

    ngOnDestroy(): void {
        this.stopAutoRefresh();
    }

    /**
     * Start auto-refresh mechanism
     */
    private startAutoRefresh(): void {
        // Create an interval that emits every 30 seconds, starting immediately
        this.refreshSubscription = interval(30000)
            .pipe(
                startWith(0), // Emit immediately on subscription
                switchMap(() => this.fetchOrderData()),
            )
            .subscribe({
                next: () => {
                    this.lastUpdated.set(new Date());
                },
                error: (error) => {
                    console.error('Error fetching order data:', error);
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Update Failed',
                        detail: 'Failed to fetch latest order status',
                    });
                },
            });
    }

    /**
     * Stop auto-refresh mechanism
     */
    private stopAutoRefresh(): void {
        if (this.refreshSubscription) {
            this.refreshSubscription.unsubscribe();
        }
    }

    /**
     * Manually refresh order data
     */
    manualRefresh(): void {
        this.fetchOrderData().subscribe();
    }

    /**
     * Fetch order data and trip data if available
     */
    private fetchOrderData() {
        this.isLoading.set(true);

        return this.orderService.getUserOrders().pipe(
            switchMap((response) => {
                if (response.data) {
                    // Find the specific order
                    const foundOrder = response.data.find(
                        (o) => o.id === this.getOrderId(),
                    );
                    if (foundOrder) {
                        // Check if status changed to CONFIRMED
                        const previousStatus = this.previousOrderStatus();
                        const currentStatus = foundOrder.status;
                        
                        // Auto-navigate if status just changed to CONFIRMED
                        if (previousStatus !== null && 
                            previousStatus !== OrderStatus.CONFIRMED && 
                            currentStatus === OrderStatus.CONFIRMED &&
                            foundOrder.tripId) {
                            
                            // Stop auto-refresh and navigate to trip info
                            this.stopAutoRefresh();
                            setTimeout(() => {
                                this.router.navigate(['/main/trip', foundOrder.tripId]);
                            }, 1000); // Small delay to show the confirmation message
                        }
                        
                        // Update signals
                        this.order.set(foundOrder);
                        this.previousOrderStatus.set(currentStatus);

                        // If order has a trip, fetch trip details
                        if (foundOrder.tripId) {
                            return this.orderService.getTripById(
                                foundOrder.tripId,
                            );
                        }
                    }
                }
                this.isLoading.set(false);
                return [];
            }),
            switchMap((tripResponse: any) => {
                if (tripResponse && tripResponse.data) {
                    this.trip.set(tripResponse.data);
                }
                this.isLoading.set(false);
                return [];
            }),
        );
    }

    /**
     * Get status severity for PrimeNG tag
     */
    getStatusSeverity(
        status: OrderStatus,
    ): 'success' | 'secondary' | 'info' | 'warn' | 'danger' | 'contrast' {
        switch (status) {
            case OrderStatus.PENDING:
                return 'info';
            case OrderStatus.SUGGESTED_FOR_DRIVER:
                return 'warn';
            case OrderStatus.CONFIRMED:
                return 'success';
            case OrderStatus.COMPLETED:
                return 'secondary';
            default:
                return 'info';
        }
    }

    /**
     * Get status icon
     */
    getStatusIcon(status: OrderStatus): string {
        switch (status) {
            case OrderStatus.PENDING:
                return 'pi-clock';
            case OrderStatus.SUGGESTED_FOR_DRIVER:
                return 'pi-send';
            case OrderStatus.CONFIRMED:
                return 'pi-check';
            case OrderStatus.COMPLETED:
                return 'pi-check-circle';
            default:
                return 'pi-info-circle';
        }
    }

    /**
     * Get trip status severity
     */
    getTripStatusSeverity(
        status: TripStatus,
    ): 'success' | 'secondary' | 'info' | 'warn' | 'danger' | 'contrast' {
        switch (status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 'warn';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 'info';
            case TripStatus.DRIVER_WITH_CLIENT:
                return 'success';
            case TripStatus.FINISHED:
                return 'secondary';
            default:
                return 'info';
        }
    }

    /**
     * Get trip status icon
     */
    getTripStatusIcon(status: TripStatus): string {
        switch (status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 'pi-exclamation-triangle';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 'pi-clock';
            case TripStatus.DRIVER_WITH_CLIENT:
                return 'pi-car';
            case TripStatus.FINISHED:
                return 'pi-check-circle';
            default:
                return 'pi-info-circle';
        }
    }

    /**
     * Get status message class
     */
    getStatusMessageClass(status: OrderStatus): string {
        switch (status) {
            case OrderStatus.PENDING:
                return 'status-pending';
            case OrderStatus.SUGGESTED_FOR_DRIVER:
                return 'status-suggested';
            case OrderStatus.CONFIRMED:
                return 'status-confirmed';
            case OrderStatus.COMPLETED:
                return 'status-completed';
            default:
                return 'status-pending';
        }
    }

    /**
     * Get user-friendly status message
     */
    getStatusMessage(status: OrderStatus): string {
        switch (status) {
            case OrderStatus.PENDING:
                return 'Your order is being processed. We are looking for available drivers.';
            case OrderStatus.SUGGESTED_FOR_DRIVER:
                return 'Your order has been suggested to a driver. Waiting for confirmation.';
            case OrderStatus.CONFIRMED:
                return 'Great! A driver has accepted your order and is on the way.';
            case OrderStatus.COMPLETED:
                return 'Your trip has been completed successfully. Thank you for using our service!';
            default:
                return 'Processing your order...';
        }
    }

    /**
     * Format date for display
     */
    formatDate(dateString: string | undefined): string {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    /**
     * Format time for display
     */
    formatTime(date: Date): string {
        return date.toLocaleTimeString();
    }

    /**
     * Navigate to full trip details page
     */
    viewTripDetails(): void {
        const tripId = this.trip()?.id;
        if (tripId) {
            this.router.navigate(['/main/trip', tripId]);
        }
    }
}
