/*
  Warnings:

  - You are about to drop the column `dropoffMarkedPlaceId` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `dropoffStopPointId` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `pickupMarkedPlaceId` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `pickupStopPointId` on the `Order` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_dropoffMarkedPlaceId_fkey";

-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_dropoffStopPointId_fkey";

-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_pickupMarkedPlaceId_fkey";

-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_pickupStopPointId_fkey";

-- DropIndex
DROP INDEX "Order_dropoffMarkedPlaceId_idx";

-- DropIndex
DROP INDEX "Order_dropoffStopPointId_idx";

-- DropIndex
DROP INDEX "Order_pickupMarkedPlaceId_idx";

-- DropIndex
DROP INDEX "Order_pickupStopPointId_idx";

-- AlterTable
ALTER TABLE "Order" DROP COLUMN "dropoffMarkedPlaceId",
DROP COLUMN "dropoffStopPointId",
DROP COLUMN "pickupMarkedPlaceId",
DROP COLUMN "pickupStopPointId",
ADD COLUMN     "lastSuggestedAt" TIMESTAMP(3),
ADD COLUMN     "lastSuggestedDriverId" TEXT;

-- CreateIndex
CREATE INDEX "Order_lastSuggestedDriverId_idx" ON "Order"("lastSuggestedDriverId");

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_lastSuggestedDriverId_fkey" FOREIGN KEY ("lastSuggestedDriverId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
