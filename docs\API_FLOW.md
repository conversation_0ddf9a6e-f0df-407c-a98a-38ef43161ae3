# API Endpoints and Flow Documentation

## Location Points

### Stop Points (Public Transport Stations)
These are predefined locations that serve as public transport stations.

**Endpoints** (`/api/stop-points`):
- `GET /` - Get all stop points
- `GET /:id` - Get specific stop point by ID
- `POST /` - Create new stop point (Auth required)

### Marked Places (User's Saved Locations)
Personal locations saved by users for quick access.

**Endpoints** (`/api/marked-places`):
- `GET /` - Get user's saved places
- `GET /:id` - Get specific marked place
- `POST /` - Save new marked place

## Order and Trip Flow

### 1. Price Calculation (New)
**Endpoint**: `POST /api/orders/calculate-price`
- User gets initial price estimate before creating order
- Uses same payload as order creation (pickup/dropoff coordinates)
- Returns calculated price in USD based on:
  - Current Syrian timezone for pricing tier
  - Valhalla route calculation for distance/time
  - Configurable rates per minute and per kilometer
- Example payload:
  ```json
  {
    "pickupLatitude": 33.5138,
    "pickupLongitude": 36.2765,
    "dropoffLatitude": 33.5018,
    "dropoffLongitude": 36.2921
  }
  ```
- Returns: `number` (e.g., 3.45 for $3.45)

### 2. Order Creation
**Endpoint**: `POST /api/orders`
- User creates an order after accepting the price
- Uses same payload as price calculation
- System calculates and stores initial price automatically
- Order includes `initialPrice` field
- Initial order status: `PENDING`

### 3. Driver Finding Nearby Orders
**Endpoint**: `POST /api/orders/nearby`
- Drivers can search for nearby pending orders
- Must provide current location (`latitude`/`longitude`)
- Returns orders within 5km radius
- Limited to 3 nearest orders
- Orders now include `initialPrice` field

### 4. Driver Accepting Order
**Endpoint**: `POST /api/orders/:orderId/approve`
- Driver accepts a specific order
- Creates a new trip
- Updates order status to `CONFIRMED`
- Initial trip status: `DRIVER_DIDNT_ARRIVE`

### 5. Driver Arrives at Pickup
**Endpoint**: `POST /api/orders/trips/:tripId/arrived`
- Driver marks arrival at pickup location
- Updates trip status to `DRIVER_WAITING_CLIENT`

### 6. Trip Start
**Endpoint**: `POST /api/orders/trips/:tripId/start`
- Driver marks that client is picked up
- Updates trip status to `DRIVER_WITH_CLIENT`

### 7. Trip Location Updates
**Endpoint**: `POST /api/orders/trips/:tripId/location`
- Driver periodically updates current location
- Requires `latitude` and `longitude`

### 8. Trip Status Check
**Endpoint**: `GET /api/orders/trips/:tripId`
- Public endpoint (no auth required)
- Returns current trip status and location
- Used for tracking active trips

### 9. Trip Completion
**Endpoint**: `POST /api/orders/trips/:tripId/complete`
- Driver marks trip as completed
- Updates trip status to `FINISHED`
- Updates order status to `COMPLETED`
- Future: This is where `finalPrice` would be calculated and stored

## Status Flows

### Order Status Flow
```
PENDING → SUGGESTED_FOR_DRIVER → CONFIRMED → COMPLETED
```

### Trip Status Flow
```
DRIVER_DIDNT_ARRIVE → DRIVER_WAITING_CLIENT → DRIVER_WITH_CLIENT → FINISHED
```

## Important Notes

1. **Improved User Experience Flow**
   - Users can now see price estimates before creating orders
   - Price calculation uses real-time routing via Valhalla
   - Syrian timezone detection for accurate pricing tiers
   - Transparent pricing reduces user abandonment

2. **Order Constraints**
   - Users can't create new orders if they have pending ones
   - Orders can only be approved by drivers with `APPROVED` status
   - Price calculation doesn't require order creation

3. **Trip Constraints**
   - Only assigned driver can update trip status
   - Status updates must follow the defined flow
   - Location updates not allowed for finished trips

4. **Security**
   - Most endpoints require authentication
   - Driver endpoints require `DriverGuard`
   - Trip status check is public for tracking purposes

5. **Pricing System**
   - Uses Valhalla for accurate route calculations
   - Configurable rates per minute and per kilometer
   - Time-based pricing (regular, night, late night)
   - Fallback to Haversine calculation if Valhalla unavailable