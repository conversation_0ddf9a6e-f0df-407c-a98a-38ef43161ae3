import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    Order,
    OrderStatus,
    CreateOrderDto,
    DriverLocationDto,
    Point,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export { OrderStatus } from '../core/types/tripoos.types';
export type {
    Order,
    CreateOrderDto,
    DriverLocationDto,
    Point,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class OrderService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Calculate the initial price for an order before creating it
     * @param createOrderDto Order creation data
     * @returns Observable with calculated price
     */
    calculateOrderPrice(
        createOrderDto: CreateOrderDto,
    ): Observable<{ data: number; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: 'api/orders/calculate-initial-order-price',
            opj: createOrderDto,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for price calculation
            failedToast: true, // Show error toast if calculation fails
        };
        return this.http.post<number>(options);
    }

    /**
     * Create a new order
     * @param createOrderDto Order creation data
     * @returns Observable with Order data
     */
    createOrder(
        createOrderDto: CreateOrderDto,
    ): Observable<{ data: Order; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            successMessage: 'Your order was successful',
            failedMessage: 'Your order failed',
            link: 'api/orders',
            opj: createOrderDto,
            des: this.destroyRef,
            showloader: false, // Disable global loading spinner
            successToast: false, // Disable success toast since we handle it manually
            failedToast: false, // Disable automatic error toast so we can show specific backend message
        };
        return this.http.post<Order>(options);
    }

    /**
     * Find nearby pending orders for drivers
     * @param locationDto Driver's current location
     * @returns Observable with array of nearby Order objects
     */
    findNearbyPendingOrders(
        locationDto: DriverLocationDto,
    ): Observable<{ data: Order[]; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: 'api/orders/nearby',
            opj: locationDto,
            des: this.destroyRef,
        };
        return this.http.post<Order[]>(options);
    }

    /**
     * Accept an order as a driver
     * @param orderId ID of the order to accept
     * @returns Observable with Trip data
     */
    approveOrder(
        orderId: string,
    ): Observable<{ data: Order; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/${orderId}/approve`,
            des: this.destroyRef,
        };
        return this.http.post<Order>(options);
    }

    /**
     * Cancel an order
     * @param orderId ID of the order to cancel
     * @returns Observable with updated Order data
     */
    cancelOrder(
        orderId: string,
    ): Observable<{ data: Order; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/${orderId}/cancel`,
            des: this.destroyRef,
        };
        return this.http.post<Order>(options);
    }

    // These functions are not implemented in the backend API
    // Removed: cancelOrder, getActiveTrip, getPendingOrders

    /**
     * Get all orders for the current user
     * @returns Observable with array of Order objects including pickup, dropoff, and trip information
     */
    getUserOrders(): Observable<
        { data: Order[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/orders/me',
            des: this.destroyRef,
        };
        return this.http.get<Order[]>(options);
    }

    getUserOrdersNotComplete(): Observable<
        { data: Order[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/orders/me/notComplete',
            des: this.destroyRef,
        };
        return this.http.get<Order[]>(options);
    }
}
