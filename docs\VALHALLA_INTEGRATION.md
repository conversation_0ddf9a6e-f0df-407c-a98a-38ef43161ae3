# Valhalla Integration for Pricing System

## Overview
The pricing system now uses Valhalla routing engine to calculate accurate travel times and distances for ride pricing. This provides more realistic pricing based on actual road networks and traffic conditions.

## Configuration

### Environment Variables
Add the following to your `.env` file:

```bash
# Valhalla Configuration
VALHALLA_BASE_URL=http://localhost:8002
```

### Default Configuration
- **Default URL**: `http://localhost:8002`
- **Costing Model**: `auto` (for car routes)
- **Units**: `kilometers`
- **Fallback**: Haversine distance calculation if Valhalla is unavailable

## Internal Usage

The pricing system is used internally by the order service. When a client creates an order, the system automatically:

1. Calls Valhalla to calculate the route
2. Determines the appropriate pricing tier based on Syrian time
3. Calculates the initial price using the formula
4. Stores the initial price with the order

## How It Works

1. **Order Creation**: When a user creates an order, the system calls Valhalla to calculate the route from pickup to dropoff
2. **Accurate Timing**: Valhalla provides realistic travel time based on road conditions
3. **Distance Calculation**: Uses actual road distance instead of straight-line distance
4. **Fallback System**: If Valhalla is unavailable, falls back to Haversine distance + estimated time

## Pricing Calculation

The new pricing formula uses Valhalla data:
```
Price = Flag Down Fee + (Valhalla Time × Rate/Min) + (Valhalla Distance × Rate/Km)
Price = max(Calculated Price, Minimum Fare)
```

## Setting Up Valhalla

### Docker Setup (Recommended)
```bash
# Pull Valhalla Docker image
docker pull valhalla/valhalla:latest

# Run Valhalla with Syria map data
docker run -d \
  --name valhalla \
  -p 8002:8002 \
  -v /path/to/map/data:/data \
  valhalla/valhalla:latest
```

### Map Data
You'll need to download Syria OSM data and build Valhalla tiles. Check the [Valhalla documentation](https://valhalla.readthedocs.io/) for detailed setup instructions.

### Testing Without Valhalla
If Valhalla is not available, the system will automatically fall back to:
- Haversine distance calculation
- Estimated time based on 30 km/h average speed
- The `success` field in the response will be `false`

## Benefits

- **More Accurate Pricing**: Based on actual road routes
- **Traffic Awareness**: Can incorporate traffic conditions
- **Better User Experience**: More predictable pricing
- **Flexible**: Easy to configure different costing models (auto, taxi, etc.)

## Troubleshooting

### Common Issues
1. **Valhalla Not Running**: Check if the service is running on the configured port
2. **Network Issues**: Ensure the application can reach the Valhalla service
3. **Map Data**: Verify that Syria map data is loaded in Valhalla

### Logs
Check application logs for Valhalla-related errors:
```bash
# Look for Valhalla service logs
grep "ValhallaService" logs/app.log
```

The system will continue to work with fallback calculations even if Valhalla is temporarily unavailable. 