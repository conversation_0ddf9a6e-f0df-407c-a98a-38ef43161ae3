export interface GeocodingResult {
    place_id: number;
    licence: string;
    osm_type: string;
    osm_id: number;
    lat: string;
    lon: string;
    display_name: string;
    boundingbox: string[];
    class: string;
    type: string;
    importance: number;
    address?: {
        road?: string;
        city?: string;
        state?: string;
        country?: string;
        postcode?: string;
        [key: string]: string | undefined;
    };
}

export interface ReverseGeocodingResult extends GeocodingResult {
    addresstype: string;
    name?: string;
}
