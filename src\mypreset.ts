import { definePreset } from '@primeng/themes';
import <PERSON> from '@primeng/themes/lara';

const MyPreset = definePreset(<PERSON>, {
  semantic: {
    colorScheme: {
      primary: {
        50: '{dark.50}',
        100: '{dark.100}',
        200: 'transparent',
        300: '{dark.300}',
        400: '{dark.400}',
        500: 'var(--background-color-200)',
        600: 'var(--background-color-200)',
        700: '{red.700}',
        800: '{dark.800}',
        900: '{dark.900}',
        950: '{dark.950}'
      },
      surface: {
        0: '{var(--background-color-200)}',
        50: '{zinc.50}',
        100: '{zinc.100}',
        200: '{zinc.200}',
        300: '{zinc.300}',
        400: '{zinc.400}',
        500: 'var(--main-color-500)',
        600: '{zinc.600}',
        700: 'var(--text-color-300)',
        800: '{dark.900}',
        900: '{zinc.900}',
        950: '{zinc.950}'
      }
    }
  }
});

export default MyPreset;
