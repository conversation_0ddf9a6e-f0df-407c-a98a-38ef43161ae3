import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface RouteResponse {
  distance: number; // in kilometers
  time: number; // in minutes
  success: boolean;
  error?: string;
}

export interface MultiPointRouteResponse {
  distance: number; // in kilometers
  time: number; // in minutes
  success: boolean;
  error?: string;
  routePoints: Array<{lat: number, lng: number}>;
  waypoints: Array<{lat: number, lng: number}>;
  segments: Array<{
    fromWaypoint: number;
    toWaypoint: number;
    distance: number;
    time: number;
    points: Array<{lat: number, lng: number}>;
  }>;
}

@Injectable()
export class ValhallaService {
  private readonly logger = new Logger(ValhallaService.name);
  private readonly valhallaBaseUrl: string;

  constructor(private configService: ConfigService) {
    // Default to local Valhalla instance, can be configured via environment
    this.valhallaBaseUrl =
      this.configService.get<string>('VALHALLA_BASE_URL') ||
      'http://localhost:8002';
  }

  /**
   * Calculate route between two points using Valhalla
   * @param fromLat Starting latitude
   * @param fromLng Starting longitude
   * @param toLat Destination latitude
   * @param toLng Destination longitude
   * @param costing Costing model (auto, taxi, bus, bicycle, pedestrian)
   * @returns Route information with distance and time
   */
  async calculateRoute(
    fromLat: number,
    fromLng: number,
    toLat: number,
    toLng: number,
    costing: string = 'auto',
  ): Promise<RouteResponse> {
    try {
      const routeRequest = {
        locations: [
          { lat: fromLat, lon: fromLng },
          { lat: toLat, lon: toLng },
        ],
        costing: costing,
        units: 'kilometers',
        language: 'en-US',
      };

      const response = await fetch(`${this.valhallaBaseUrl}/route`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(routeRequest),
      });

      if (!response.ok) {
        throw new Error(
          `Valhalla API error: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();

      if (!data.trip || !data.trip.legs || data.trip.legs.length === 0) {
        throw new Error('Invalid route response from Valhalla');
      }

      const leg = data.trip.legs[0];
      const distanceKm = leg.length; // Valhalla returns distance in kilometers when units=kilometers
      const timeMinutes = leg.time / 60; // Valhalla returns time in seconds, convert to minutes

      return {
        distance: distanceKm,
        time: timeMinutes,
        success: true,
      };
    } catch (error) {
      this.logger.error(
        `Failed to calculate route: ${error.message}`,
        error.stack,
      );

      // Fallback to Haversine calculation if Valhalla fails
      const fallbackDistance = this.calculateHaversineDistance(
        fromLat,
        fromLng,
        toLat,
        toLng,
      );
      const fallbackTime = this.estimateTimeFromDistance(fallbackDistance);

      return {
        distance: fallbackDistance,
        time: fallbackTime,
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Fallback Haversine distance calculation
   */
  private calculateHaversineDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  }

  /**
   * Estimate time from distance (fallback calculation)
   * Assumes average city driving speed of 30 km/h
   */
  private estimateTimeFromDistance(distanceKm: number): number {
    const averageSpeedKmh = 30; // Average city driving speed
    return (distanceKm / averageSpeedKmh) * 60; // Convert hours to minutes
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Calculate multi-point route using Valhalla
   * @param waypoints Array of waypoints to visit in order
   * @returns Multi-point route information with detailed route points
   */
  async calculateMultiPointRoute(
    waypoints: Array<{lat: number, lng: number}>,
  ): Promise<MultiPointRouteResponse> {
    try {
      const routeRequest = {
        locations: waypoints.map((point, index) => ({
          lat: point.lat,
          lon: point.lng,
          type: index === 0 || index === waypoints.length - 1 ? "break" : "via"
        })),
        costing: 'taxi',
        units: 'kilometers',
        language: 'en-US',
        directions_options: {
          units: 'kilometers',
          language: 'en-US'
        }
      };

      const response = await fetch(`${this.valhallaBaseUrl}/route`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(routeRequest),
      });

      if (!response.ok) {
        throw new Error(`Valhalla API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.trip || !data.trip.legs || data.trip.legs.length === 0) {
        throw new Error('Invalid route response from Valhalla');
      }

      // Decode the route shape to get all points
      const routePoints = this.decodePolyline(data.trip.legs[0].shape);
      
      // Calculate total distance and time
      const totalDistance = data.trip.legs[0].summary.length;
      const totalTime = data.trip.legs[0].summary.time / 60; // Convert to minutes

      // Create segments between waypoints
      const segments = this.createSegmentsFromManeuvers(
        data.trip.legs[0].maneuvers,
        routePoints
      );

      return {
        distance: totalDistance,
        time: totalTime,
        success: true,
        routePoints: routePoints,
        waypoints: waypoints,
        segments: segments
      };

    } catch (error) {
      this.logger.error(`Failed to calculate multi-point route: ${error.message}`);
      throw error;
    }
  }

  /**
   * Decode Google's polyline format to get route points
   */
  private decodePolyline(encoded: string): Array<{lat: number, lng: number}> {
    const poly = [];
    let index = 0, len = encoded.length;
    let lat = 0, lng = 0;

    while (index < len) {
      let shift = 0, result = 0;

      do {
        let b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (result >= 0x20);

      let dlat = ((result & 1) ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;

      do {
        let b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (result >= 0x20);

      let dlng = ((result & 1) ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      poly.push({lat: lat / 1e5, lng: lng / 1e5});
    }

    return poly;
  }

  /**
   * Create segments from Valhalla maneuvers
   */
  private createSegmentsFromManeuvers(
    maneuvers: any[],
    routePoints: Array<{lat: number, lng: number}>
  ): Array<{
    fromWaypoint: number;
    toWaypoint: number;
    distance: number;
    time: number;
    points: Array<{lat: number, lng: number}>;
  }> {
    const segments = [];
    
    for (let i = 0; i < maneuvers.length; i++) {
      const maneuver = maneuvers[i];
      const segmentPoints = routePoints.slice(
        maneuver.begin_shape_index,
        maneuver.end_shape_index + 1
      );

      segments.push({
        fromWaypoint: i,
        toWaypoint: i + 1,
        distance: maneuver.length,
        time: maneuver.time / 60, // Convert to minutes
        points: segmentPoints
      });
    }

    return segments;
  }
}
