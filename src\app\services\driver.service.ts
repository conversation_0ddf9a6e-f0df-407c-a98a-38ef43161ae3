import { DestroyRef, Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import {
    CreateDriverDto,
    DriverFiles,
    DriverStatusUpdateDto,
} from '../core/types/tripoos.types';
import { HttpService, requestOptions } from './http.service';

// Re-export types for convenience
export { DriverStatus } from '../core/types/tripoos.types';
export type {
    CreateDriverDto,
    DriverFiles,
    DriverStatusUpdateDto,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class DriverService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Submit application to become a driver
     * @param createDriverDto Driver application data
     * @param files Driver document files
     * @returns Observable with driver application status
     */
    becomeDriver(
        createDriverDto: CreateDriverDto,
        files: DriverFiles | any,
    ): Observable<{ data: any; error: null } | { data: null; error: any }> {
        // For file uploads, we need to create a FormData object
        const formData = new FormData();

        // Add driver data
        formData.append('make', createDriverDto.make);
        formData.append('model', createDriverDto.model);
        formData.append('year', createDriverDto.year.toString());
        if (createDriverDto.licensePlate) {
            formData.append('licensePlate', createDriverDto.licensePlate);
        }

        // Add files
        if (files.idFront) {
            formData.append('idFront', files.idFront);
        }
        if (files.idBack) {
            formData.append('idBack', files.idBack);
        }
        if (files.personalPhoto) {
            formData.append('personalPhoto', files.personalPhoto);
        }
        if (files.carPhotos && files.carPhotos.length > 0) {
            files.carPhotos.forEach((photo: any) => {
                formData.append('carPhotos', photo);
            });
        }

        const options: requestOptions = {
            link: 'api/driver/become-driver',
            opj: formData,
            des: this.destroyRef,
            extra: {
                // Don't set Content-Type header, browser will set it with boundary for FormData
                headers: {},
            },
        };

        return this.http.post(options);
    }

    /**
     * Update driver status
     * @param userId ID of the driver
     * @param statusUpdateDto Status update data
     * @returns Observable with updated driver status
     */
    updateDriverStatus(
        userId: string,
        statusUpdateDto: DriverStatusUpdateDto,
    ): Observable<{ data: any; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/driver/${userId}/status`,
            opj: statusUpdateDto,
            des: this.destroyRef,
        };
        return this.http.patch(options);
    }
}
