import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { LatLng } from 'leaflet';
import {
    LocateResponse,
    HeightResponse,
    IsochroneRequest,
    IsochroneResponse,
    DirectionsRequest,
    DirectionsResponse,
    ServerStatus,
    LocateRequest,
    HeightRequest,
} from '../shared/types/valhalla.types';

@Injectable({
    providedIn: 'root',
})
export class ValhallaService {
    private readonly VALHALLA_URL = environment.valhallaUrl;
    private readonly headers = new HttpHeaders()
        .set('Content-Type', 'application/json')
        .set('accept-language', 'ar-sa');

    constructor(private http: HttpClient) {}

    /**
     * Get location information
     * @param latLng - The latitude and longitude coordinates
     * @param profile - The routing profile (e.g., 'car', 'bicycle', 'pedestrian')
     */
    getLocate(latLng: LatLng, profile: string): Observable<LocateResponse> {
        this.headers.set('accept-language', 'ar-sa');
        const request = this.buildLocateRequest(latLng, profile);
        return this.http.post<LocateResponse>(
            `${this.VALHALLA_URL}/locate`,
            request,
            { headers: this.headers },
        );
    }

    /**
     * Get height/elevation data for a single point
     * @param latLng - The latitude and longitude coordinates
     */
    getHeight(latLng: LatLng): Observable<HeightResponse> {
        this.headers.set('accept-language', 'ar-sa');

        const request = this.buildHeightRequest([[latLng.lat, latLng.lng]]);
        return this.http.post<HeightResponse>(
            `${this.VALHALLA_URL}/height`,
            request,
            { headers: this.headers },
        );
    }

    /**
     * Get height data for a route
     * @param coordinates - Array of [lat, lng] coordinates
     */
    getRouteHeight(
        coordinates: [number, number][],
    ): Observable<HeightResponse> {
        this.headers.set('accept-language', 'ar-sa');

        const request = this.buildHeightRequest(coordinates);
        return this.http.post<HeightResponse>(
            `${this.VALHALLA_URL}/height`,
            request,
            { headers: this.headers },
        );
    }

    /**
     * Get isochrone data
     * @param request - The isochrone request parameters
     */
    getIsochrones(request: IsochroneRequest): Observable<IsochroneResponse> {
        const params = new HttpParams().set('json', JSON.stringify(request));
        this.headers.set('accept-language', 'ar-sa');

        return this.http.get<IsochroneResponse>(
            `${this.VALHALLA_URL}/isochrone`,
            { params, headers: this.headers },
        );
    }

    /**
     * Get routing directions
     * @param request - The directions request parameters
     */

    getDirections(request: DirectionsRequest): Observable<DirectionsResponse> {
        const params = new HttpParams().set('json', JSON.stringify(request));

        this.headers.set('accept-language', 'ar-sa');

        return this.http.get<DirectionsResponse>(`${this.VALHALLA_URL}/route`, {
            params,
            headers: this.headers,
        });
    }

    /**
     * Get server status
     */
    getStatus(): Observable<ServerStatus> {
        return this.http.get<ServerStatus>(`${this.VALHALLA_URL}/status`);
    }

    private buildLocateRequest(latLng: LatLng, profile: string): LocateRequest {
        this.headers.set('accept-language', 'ar-sa');

        const valhallaProfile = profile === 'car' ? 'auto' : profile;
        return {
            costing: valhallaProfile,
            locations: [{ lat: latLng.lat, lon: latLng.lng }],
        };
    }

    private buildHeightRequest(latLngs: [number, number][]): HeightRequest {
        this.headers.set('accept-language', 'ar-sa');

        const shape = latLngs.map(([lat, lng]) => ({ lat, lon: lng }));
        return {
            range: latLngs.length > 1,
            shape,
            id: 'valhalla_height',
        };
    }
}
