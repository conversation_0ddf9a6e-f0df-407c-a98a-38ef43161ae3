<div class="trip-info-container">
    @if (isLoading() && !trip()) {
        <div class="loading-state">
            <p-progressSpinner
                strokeWidth="2"
                [style]="{ width: '50px', height: '50px' }"
            ></p-progressSpinner>
            <p>Loading trip information...</p>
        </div>
    } @else if (trip() && order()) {
        <!-- Special UI for DRIVER_DIDNT_ARRIVE, DRIVER_WAITING_CLIENT, and DRIVER_WITH_CLIENT status -->
        @if (
            trip()!.status === TripStatus.DRIVER_DIDNT_ARRIVE ||
            trip()!.status === TripStatus.DRIVER_WAITING_CLIENT ||
            trip()!.status === TripStatus.DRIVER_WITH_CLIENT
        ) {
            <div class="driver-didnt-arrive-container">
                <!-- Minimalist Header -->
                <div class="minimalist-header">
                    <h2>Order Details</h2>
                    <div class="status-message">
                        @if (isDriver()) {
                            @switch (trip()!.status) {
                                @case (TripStatus.DRIVER_DIDNT_ARRIVE) {
                                    <span>Navigate to pickup location</span>
                                }
                                @case (TripStatus.DRIVER_WAITING_CLIENT) {
                                    <span>Waiting for client</span>
                                }
                                @case (TripStatus.DRIVER_WITH_CLIENT) {
                                    <span>Trip in progress</span>
                                }
                                @case (TripStatus.FINISHED) {
                                    <span>Trip completed</span>
                                }
                            }
                        } @else {
                            @switch (trip()!.status) {
                                @case (TripStatus.DRIVER_DIDNT_ARRIVE) {
                                    <span>Driver is on the way</span>
                                }
                                @case (TripStatus.DRIVER_WAITING_CLIENT) {
                                    <span>Driver has arrived</span>
                                }
                                @case (TripStatus.DRIVER_WITH_CLIENT) {
                                    <span>Trip in progress</span>
                                }
                                @case (TripStatus.FINISHED) {
                                    <span>Trip completed</span>
                                }
                            }
                        }
                    </div>
                </div>

                <!-- Map Section -->
                <div class="map-section flex flex-col">
                    <app-map
                        [nodes]="mapNodes()"
                        [options]="{
                            center: mapCenter(),
                            zoom: mapZoom(),
                        }"
                    >
                    </app-map>
                </div>

                <!-- Trip Info (Minimalist) -->
                <div class="trip-info-minimal">
                    <div class="info-row">
                        <span class="label">📍 Pickup:</span>
                        <span class="value">
                            @if (pickupAddressLoading()) {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            } @else if (pickupAddress()) {
                                {{ pickupAddress() }}
                            } @else {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            }
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">🏁 Destination:</span>
                        <span class="value">
                            @if (dropoffAddressLoading()) {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            } @else if (dropoffAddress()) {
                                {{ dropoffAddress() }}
                            } @else {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            }
                        </span>
                    </div>
                    @if (isDriver()) {
                        <div class="info-row">
                            <span class="label">👤 Client:</span>
                            <span class="value"
                                >{{ order()!.user?.firstName }}
                                {{ order()!.user?.lastName }}</span
                            >
                        </div>
                    } @else {
                        <div class="info-row">
                            <span class="label">🚗 Driver:</span>
                            <span class="value"
                                >{{ trip()!.driver?.firstName }}
                                {{ trip()!.driver?.lastName }}</span
                            >
                        </div>
                    }
                </div>

                <!-- Timing Components for DRIVER_WAITING_CLIENT -->
                @if (trip()!.status === TripStatus.DRIVER_WAITING_CLIENT) {
                    <div class="timing-section">
                        @if (
                            trip()!.timeTillDriverArrived !== undefined &&
                            trip()!.timeTillDriverArrived !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">
                                    Driver Arrival Time
                                </div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!.timeTillDriverArrived || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                    </div>
                }

                <!-- Timing Components for DRIVER_WITH_CLIENT -->
                @if (trip()!.status === TripStatus.DRIVER_WITH_CLIENT) {
                    <div class="timing-section">
                        @if (
                            trip()!.timeTillDriverArrived !== undefined &&
                            trip()!.timeTillDriverArrived !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">
                                    Driver Arrival Time
                                </div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!.timeTillDriverArrived || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                        @if (
                            trip()!.timeDriverWaitingForClient !== undefined &&
                            trip()!.timeDriverWaitingForClient !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">
                                    Driver Waiting Time
                                </div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!
                                                .timeDriverWaitingForClient || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                    </div>
                }

                <!-- Participants Section -->
                <div class="participants">
                    @if (isDriver()) {
                        <!-- Show client info to driver -->
                        <div class="participant client-card">
                            <div class="participant-details">
                                <h3>Client:</h3>
                                <p class="name">
                                    {{ order()!.user?.firstName }}
                                    {{ order()!.user?.lastName }}
                                </p>
                                <div class="contact-actions">
                                    <button
                                        pButton
                                        icon="pi pi-phone"
                                        class="contact-btn"
                                        [pTooltip]="'Call Client'"
                                        (click)="callClient()"
                                    ></button>
                                    <button
                                        pButton
                                        icon="pi pi-comment"
                                        class="contact-btn"
                                        [pTooltip]="'Message Client'"
                                        (click)="messageClient()"
                                    ></button>
                                </div>
                            </div>
                        </div>
                    } @else {
                        <!-- Show driver info to client -->
                        <div class="participant driver-card">
                            <div class="participant-details">
                                <h3>Driver:</h3>
                                <p class="name">
                                    {{ trip()!.driver?.firstName }}
                                    {{ trip()!.driver?.lastName }}
                                </p>
                                @if (trip()!.driver?.car) {
                                    <div class="car-details">
                                        <p class="car-info">
                                            {{ trip()!.driver?.car?.make }}
                                            {{ trip()!.driver?.car?.model }}
                                        </p>
                                        <p class="car-year">
                                            ({{ trip()!.driver?.car?.year }})
                                        </p>
                                        @if (
                                            trip()!.driver?.car?.licensePlate
                                        ) {
                                            <div class="license-plate">
                                                {{
                                                    trip()!.driver?.car
                                                        ?.licensePlate
                                                }}
                                            </div>
                                        }
                                    </div>
                                }
                                <div class="contact-actions">
                                    <button
                                        pButton
                                        icon="pi pi-phone"
                                        class="contact-btn"
                                        [pTooltip]="'Call Driver'"
                                        (click)="callDriver()"
                                    ></button>
                                    <button
                                        pButton
                                        icon="pi pi-comment"
                                        class="contact-btn"
                                        [pTooltip]="'Message Driver'"
                                        (click)="messageDriver()"
                                    ></button>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Action Buttons (Driver Only) -->
                @if (isDriver()) {
                    <div class="action-buttons-minimal">
                        @if (
                            trip()!.status === TripStatus.DRIVER_DIDNT_ARRIVE
                        ) {
                            <!-- Arrive Swipe Button -->
                            <div
                                class="swipe-container"
                                [class.swipe-completed]="isSwipeCompleted()"
                                (mousedown)="startSlide($event)"
                                (touchstart)="startSlide($event)"
                                (mousemove)="onSlide($event)"
                                (touchmove)="onSlide($event)"
                                (mouseup)="endSlide($event)"
                                (touchend)="endSlide($event)"
                                (mouseleave)="endSlide($event)"
                            >
                                <div class="swipe-track">
                                    <div
                                        class="swipe-progress"
                                        [style.width.%]="getSwipeProgress()"
                                    ></div>
                                    <div class="swipe-target">
                                        <span class="target-text">Arrive</span>
                                        <span class="target-icon">📍</span>
                                    </div>
                                </div>
                                <div
                                    class="swipe-handle"
                                    [style.left.%]="getHandlePosition()"
                                    [class.swipe-active]="isSliding()"
                                >
                                    <span class="handle-icon">🚗</span>
                                </div>
                                @if (isActionInProgress()) {
                                    <div class="swipe-loading">
                                        <p-progressSpinner
                                            [style]="{
                                                width: '20px',
                                                height: '20px',
                                            }"
                                        ></p-progressSpinner>
                                    </div>
                                }
                            </div>
                        } @else if (
                            trip()!.status === TripStatus.DRIVER_WAITING_CLIENT
                        ) {
                            <!-- Client Packed Swipe Button -->
                            <div
                                class="swipe-container"
                                [class.swipe-completed]="isSwipeCompleted()"
                                (mousedown)="startSlide($event)"
                                (touchstart)="startSlide($event)"
                                (mousemove)="onSlide($event)"
                                (touchmove)="onSlide($event)"
                                (mouseup)="endSlide($event)"
                                (touchend)="endSlide($event)"
                                (mouseleave)="endSlide($event)"
                            >
                                <div class="swipe-track">
                                    <div
                                        class="swipe-progress"
                                        [style.width.%]="getSwipeProgress()"
                                    ></div>
                                    <div class="swipe-target">
                                        <span class="target-text"
                                            >Client Packed</span
                                        >
                                    </div>
                                </div>
                                <div
                                    class="swipe-handle"
                                    [style.left.%]="getHandlePosition()"
                                    [class.swipe-active]="isSliding()"
                                >
                                    <span class="handle-icon">👤</span>
                                </div>
                                @if (isActionInProgress()) {
                                    <div class="swipe-loading">
                                        <p-progressSpinner
                                            [style]="{
                                                width: '20px',
                                                height: '20px',
                                            }"
                                        ></p-progressSpinner>
                                    </div>
                                }
                            </div>
                        } @else if (
                            trip()!.status === TripStatus.DRIVER_WITH_CLIENT
                        ) {
                            <!-- Complete Trip Swipe Button -->
                            <div
                                class="swipe-container"
                                [class.swipe-completed]="isSwipeCompleted()"
                                (mousedown)="startSlide($event)"
                                (touchstart)="startSlide($event)"
                                (mousemove)="onSlide($event)"
                                (touchmove)="onSlide($event)"
                                (mouseup)="endSlide($event)"
                                (touchend)="endSlide($event)"
                                (mouseleave)="endSlide($event)"
                            >
                                <div class="swipe-track">
                                    <div
                                        class="swipe-progress"
                                        [style.width.%]="getSwipeProgress()"
                                    ></div>
                                    <div class="swipe-target">
                                        <span class="target-text"
                                            >Complete Trip</span
                                        >
                                    </div>
                                </div>
                                <div
                                    class="swipe-handle"
                                    [style.left.%]="getHandlePosition()"
                                    [class.swipe-active]="isSliding()"
                                >
                                    <span class="handle-icon">🏁</span>
                                </div>
                                @if (isActionInProgress()) {
                                    <div class="swipe-loading">
                                        <p-progressSpinner
                                            [style]="{
                                                width: '20px',
                                                height: '20px',
                                            }"
                                        ></p-progressSpinner>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                }
            </div>
        } @else {
            <!-- Regular Trip UI for other statuses -->
            <div class="trip-card">
                <!-- Header -->
                <div class="trip-header">
                    <div class="trip-title">
                        <h1>Trip #{{ trip()!.id.substring(0, 8) }}</h1>
                        <div class="status-badge" [ngClass]="getStatusClass()">
                            {{ getStatusText() }}
                        </div>
                    </div>
                    <div class="trip-meta">
                        <span>{{ formatDate(trip()!.createdAt) }}</span>
                    </div>
                </div>

                <!-- Trip Timing Information -->
                @if (trip()!.status === TripStatus.FINISHED) {
                    <div class="timing-section">
                        <!-- Final Price Display -->
                        @if (
                            order()!.finalPrice !== undefined &&
                            order()!.finalPrice !== null
                        ) {
                            <div class="final-price-display">
                                <div class="price-label">Final Price</div>
                                <div class="price-value">
                                    ${{ order()!.finalPrice!.toFixed(2) }}
                                </div>
                            </div>
                        }

                        @if (
                            trip()!.timeTillDriverArrived !== undefined &&
                            trip()!.timeTillDriverArrived !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">
                                    Driver Arrival Time
                                </div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!.timeTillDriverArrived || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                        @if (
                            trip()!.timeDriverWaitingForClient !== undefined &&
                            trip()!.timeDriverWaitingForClient !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">Driver Wait Time</div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!
                                                .timeDriverWaitingForClient || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                        @if (
                            trip()!.actualTripTime !== undefined &&
                            trip()!.actualTripTime !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">Trip Duration</div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!.actualTripTime || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                    </div>
                }

                <!-- Timing for DRIVER_WAITING_CLIENT status -->
                @if (trip()!.status === TripStatus.DRIVER_WAITING_CLIENT) {
                    <div class="timing-section">
                        @if (
                            trip()!.timeTillDriverArrived !== undefined &&
                            trip()!.timeTillDriverArrived !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">
                                    Driver Arrival Time
                                </div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!.timeTillDriverArrived || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                    </div>
                }

                <!-- Timing for DRIVER_WITH_CLIENT status -->
                @if (trip()!.status === TripStatus.DRIVER_WITH_CLIENT) {
                    <div class="timing-section">
                        @if (
                            trip()!.timeTillDriverArrived !== undefined &&
                            trip()!.timeTillDriverArrived !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">
                                    Driver Arrival Time
                                </div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!.timeTillDriverArrived || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                        @if (
                            trip()!.timeDriverWaitingForClient !== undefined &&
                            trip()!.timeDriverWaitingForClient !== null
                        ) {
                            <div class="timing-item">
                                <div class="timing-label">
                                    Driver Waiting Time
                                </div>
                                <div class="timing-value">
                                    {{
                                        formatTimeMMSS(
                                            trip()!
                                                .timeDriverWaitingForClient || 0
                                        )
                                    }}
                                </div>
                            </div>
                        }
                    </div>
                }

                <!-- Route Information -->
                <div class="route-info">
                    <div class="location-item pickup">
                        <div class="location-icon">
                            <i class="pi pi-map-marker"></i>
                        </div>
                        <div class="location-details">
                            <h3>Pickup Location</h3>
                            <p>
                                @if (pickupAddressLoading()) {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                } @else if (pickupAddress()) {
                                    {{ pickupAddress() }}
                                } @else {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                }
                            </p>
                        </div>
                    </div>
                    <div class="route-line">
                        <div
                            class="route-progress"
                            [style.height.%]="getProgressPercentage()"
                        ></div>
                    </div>
                    <div class="location-item dropoff">
                        <div class="location-icon">
                            <i class="pi pi-flag"></i>
                        </div>
                        <div class="location-details">
                            <h3>Destination</h3>
                            <p>
                                @if (dropoffAddressLoading()) {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                } @else if (dropoffAddress()) {
                                    {{ dropoffAddress() }}
                                } @else {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                }
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Participants -->
                <div class="participants">
                    @if (isDriver()) {
                        <!-- Show client info to driver -->
                        <div class="participant client-card">
                            <div class="participant-details">
                                <h3>Client:</h3>
                                <p class="name">
                                    {{ order()!.user?.firstName }}
                                    {{ order()!.user?.lastName }}
                                </p>
                                <div class="contact-actions">
                                    <button
                                        pButton
                                        icon="pi pi-phone"
                                        class="contact-btn"
                                        [pTooltip]="'Call Client'"
                                        (click)="callClient()"
                                    ></button>
                                    <button
                                        pButton
                                        icon="pi pi-comment"
                                        class="contact-btn"
                                        [pTooltip]="'Message Client'"
                                        (click)="messageClient()"
                                    ></button>
                                </div>
                            </div>
                        </div>
                    } @else {
                        <!-- Show driver info to client -->
                        <div class="participant driver-card">
                            <div class="participant-details">
                                <h3>Driver:</h3>
                                <p class="name">
                                    {{ trip()!.driver?.firstName }}
                                    {{ trip()!.driver?.lastName }}
                                </p>
                                @if (trip()!.driver?.car) {
                                    <div class="car-details">
                                        <p class="car-info">
                                            {{ trip()!.driver?.car?.make }}
                                            {{ trip()!.driver?.car?.model }}
                                        </p>
                                        <p class="car-year">
                                            ({{ trip()!.driver?.car?.year }})
                                        </p>
                                        @if (
                                            trip()!.driver?.car?.licensePlate
                                        ) {
                                            <div class="license-plate">
                                                {{
                                                    trip()!.driver?.car
                                                        ?.licensePlate
                                                }}
                                            </div>
                                        }
                                    </div>
                                }
                                <div class="contact-actions">
                                    <button
                                        pButton
                                        icon="pi pi-phone"
                                        class="contact-btn"
                                        [pTooltip]="'Call Driver'"
                                        (click)="callDriver()"
                                    ></button>
                                    <button
                                        pButton
                                        icon="pi pi-comment"
                                        class="contact-btn"
                                        [pTooltip]="'Message Driver'"
                                        (click)="messageDriver()"
                                    ></button>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Current Status Message -->
                @if (trip()!.status !== TripStatus.FINISHED) {
                    <div
                        class="status-message"
                        [ngClass]="getStatusMessageClass()"
                    >
                        <div class="status-content">
                            @if (isActionInProgress()) {
                                <p-progressSpinner
                                    strokeWidth="2"
                                    [style]="{
                                        width: '20px',
                                        height: '20px',
                                    }"
                                ></p-progressSpinner>
                            }
                            <div class="status-text">
                                <h3>{{ getStatusTitle() }}</h3>
                                <p>{{ getStatusDescription() }}</p>
                                @if (
                                    trip()!.currentLocationLatitude &&
                                    trip()!.currentLocationLongitude &&
                                    isDriver()
                                ) {
                                    <p class="location-info">
                                        📍 Current location:
                                        {{
                                            trip()!.currentLocationLatitude!.toFixed(
                                                6
                                            )
                                        }},
                                        {{
                                            trip()!.currentLocationLongitude!.toFixed(
                                                6
                                            )
                                        }}
                                    </p>
                                }
                            </div>
                        </div>
                    </div>
                }

                <!-- Action Buttons (Driver Only) -->
                @if (isDriver() && canPerformAction()) {
                    <div class="action-buttons">
                        @switch (trip()!.status) {
                            @case (TripStatus.DRIVER_DIDNT_ARRIVE) {
                                <button
                                    pButton
                                    label="✓ Mark as Arrived"
                                    icon="pi pi-map-marker"
                                    class="action-btn primary"
                                    [loading]="isActionInProgress()"
                                    (click)="confirmMarkArrived()"
                                ></button>
                                <button
                                    pButton
                                    label="📍 Update Location"
                                    icon="pi pi-refresh"
                                    class="action-btn secondary"
                                    [loading]="isUpdatingLocation()"
                                    (click)="updateLocation()"
                                ></button>
                            }
                            @case (TripStatus.DRIVER_WAITING_CLIENT) {
                                <button
                                    pButton
                                    label="🚀 Start Trip"
                                    icon="pi pi-play"
                                    class="action-btn primary pulse"
                                    [loading]="isActionInProgress()"
                                    (click)="confirmStartTrip()"
                                ></button>
                            }
                            @case (TripStatus.DRIVER_WITH_CLIENT) {
                                <button
                                    pButton
                                    label="🏁 Complete Trip"
                                    icon="pi pi-check"
                                    class="action-btn primary"
                                    [loading]="isActionInProgress()"
                                    (click)="confirmCompleteTrip()"
                                ></button>
                                <button
                                    pButton
                                    label="📍 Update Location"
                                    icon="pi pi-refresh"
                                    class="action-btn secondary"
                                    [loading]="isUpdatingLocation()"
                                    (click)="updateLocation()"
                                ></button>
                            }
                        }
                    </div>
                }
            </div>
        }
    } @else {
        <div class="error-state">
            <h2>Trip Not Found</h2>
            <p>
                The requested trip could not be found or you don't have
                permission to view it.
            </p>
            <button
                pButton
                label="Go Back"
                class="action-btn secondary"
                (click)="goBack()"
            ></button>
        </div>
    }
</div>
