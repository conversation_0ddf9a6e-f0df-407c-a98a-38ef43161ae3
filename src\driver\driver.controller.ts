import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFiles,
  Body,
  Param,
  Parse<PERSON><PERSON><PERSON>ip<PERSON>,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { DriverService } from './driver.service';
import { CreateDriverDto } from './dto/createDriverDto.dto';
import { DriverStatus, User } from '@prisma/client';
import { multerOptions } from 'src/common/config/multer.config';
import { GetUser } from 'src/common/decorators';
import { AuthGuard } from 'src/common/guards';

@Controller('api/driver')
export class DriverController {
  constructor(private readonly driverService: DriverService) {}

  @UseGuards(AuthGuard)
  @Post('become-driver')
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: 'idFront', maxCount: 1 },
        { name: 'idBack', maxCount: 1 },
        { name: 'personalPhoto', maxCount: 1 },
        { name: 'carPhotos', maxCount: 10 },
      ],
      multerOptions,
    ),
  )
  async becomeDriver(
    @GetUser() user: User,
    @UploadedFiles()
    files: {
      idFront?: Express.Multer.File[];
      idBack?: Express.Multer.File[];
      personalPhoto?: Express.Multer.File[];
      carPhotos?: Express.Multer.File[];
    },
    @Body() createDriverDto: CreateDriverDto,
  ) {
    if (typeof createDriverDto.year === 'string') {
      createDriverDto.year = parseInt(createDriverDto.year, 10);
    }
    return this.driverService.processDriverUpload(files, createDriverDto, user);
  }

  @Patch(':userId/status')
  async updateDriverStatus(
    @Param('userId', new ParseUUIDPipe()) userId: string,
    @Body() body: { currentStatus: DriverStatus; newStatus: DriverStatus },
  ) {
    return this.driverService.updateDriverStatus(
      userId,
      body.currentStatus,
      body.newStatus,
    );
  }
}
