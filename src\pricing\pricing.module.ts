import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../common/prisma/prisma.module';
import { PricingService } from './pricing.service';
import { ValhallaService } from './valhalla.service';

@Module({
  imports: [PrismaModule, ConfigModule],
  providers: [PricingService, ValhallaService],
  exports: [PricingService],
})
export class PricingModule {}
