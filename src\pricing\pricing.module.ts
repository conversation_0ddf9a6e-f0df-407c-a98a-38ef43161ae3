import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../common/prisma/prisma.module';
import { PricingService } from './pricing.service';
import { ValhallaService } from './valhalla.service';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    JwtModule.register({})
  ],
  providers: [PricingService, ValhallaService],
  exports: [PricingService, ValhallaService],
})
export class PricingModule {}
