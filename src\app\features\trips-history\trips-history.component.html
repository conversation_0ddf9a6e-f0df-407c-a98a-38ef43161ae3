<div class="min-h-screen bg-background-color-100 p-4 md:p-6">
    <div class="mx-auto">
        <div class="mb-8 ms-8">
            <h1 class="mb-2 text-3xl font-bold text-text-color-100">
                Trip History
            </h1>
            <p class="text-text-color-300">View and manage your past trips</p>
        </div>

        @if (orders.length === 0) {
            <div class="flex min-h-[400px] items-center justify-center">
                <div class="text-center">
                    <i
                        class="pi pi-calendar-times mb-4 text-6xl text-text-color-400"
                    ></i>
                    <h3 class="mb-2 text-xl font-semibold text-text-color-200">
                        No trips yet
                    </h3>
                    <p class="text-text-color-300">
                        Your trip history will appear here once you start
                        traveling
                    </p>
                </div>
            </div>
        } @else {
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                @for (order of orders; track order.id) {
                    <div
                        class="overflow-hidden rounded-xl border border-background-color-300 bg-white shadow-shadow-200 transition-all duration-300 hover:-translate-y-1 hover:shadow-shadow-400"
                    >
                        <!-- Card Header -->
                        <div class="bg-background-color-200 p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3
                                        class="mb-1 text-lg font-semibold text-white"
                                    >
                                        Trip #{{ order.id.substring(0, 8) }}
                                    </h3>
                                    <p class="text-sm text-main-color-100">
                                        {{ formatDate(order.createdAt) }}
                                    </p>
                                </div>
                                <span
                                    class="px-3 py-1 rounded-full text-xs font-medium {{
                                        getStatusClass(order.status)
                                    }}"
                                >
                                    {{ getStatusLabel(order.status) }}
                                </span>
                            </div>
                        </div>

                        <!-- Card Content -->
                        <div class="space-y-4 p-4">
                            <!-- Route Information -->
                            <div class="space-y-3">
                                <div class="flex items-start space-x-3">
                                    <div
                                        class="mt-1 h-3 w-3 flex-shrink-0 rounded-full bg-green-500"
                                    ></div>
                                    <div class="flex-1">
                                        <p
                                            class="text-sm font-medium text-text-color-200"
                                        >
                                            Pickup
                                        </p>
                                        @if (order.pickupPoint) {
                                            <p
                                                class="text-xs text-text-color-300"
                                            >
                                                {{
                                                    order.pickupPoint.latitude.toFixed(
                                                        4
                                                    )
                                                }},
                                                {{
                                                    order.pickupPoint.longitude.toFixed(
                                                        4
                                                    )
                                                }}
                                            </p>
                                        } @else {
                                            <p
                                                class="text-xs text-text-color-300"
                                            >
                                                Location not available
                                            </p>
                                        }
                                    </div>
                                </div>

                                <div class="flex items-start space-x-3">
                                    <div
                                        class="mt-1 h-3 w-3 flex-shrink-0 rounded-full bg-red-500"
                                    ></div>
                                    <div class="flex-1">
                                        <p
                                            class="text-sm font-medium text-text-color-200"
                                        >
                                            Dropoff
                                        </p>
                                        @if (order.dropoffPoint) {
                                            <p
                                                class="text-xs text-text-color-300"
                                            >
                                                {{
                                                    order.dropoffPoint.latitude.toFixed(
                                                        4
                                                    )
                                                }},
                                                {{
                                                    order.dropoffPoint.longitude.toFixed(
                                                        4
                                                    )
                                                }}
                                            </p>
                                        } @else {
                                            <p
                                                class="text-xs text-text-color-300"
                                            >
                                                Location not available
                                            </p>
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Trip Details -->
                            <div
                                class="space-y-2 border-t border-background-color-300 pt-4"
                            >
                                @if (order.tripId) {
                                    <div
                                        class="flex items-center justify-between"
                                    >
                                        <span
                                            class="text-sm text-text-color-300"
                                            >Trip ID</span
                                        >
                                        <span
                                            class="text-sm font-medium text-text-color-200"
                                            >{{
                                                order.tripId.substring(0, 8)
                                            }}</span
                                        >
                                    </div>
                                }

                                @if (order.trip?.driver) {
                                    <div
                                        class="flex items-center justify-between"
                                    >
                                        <span
                                            class="text-sm text-text-color-300"
                                            >Driver</span
                                        >
                                        <span
                                            class="text-sm font-medium text-text-color-200"
                                        >
                                            {{ order.trip?.driver?.firstName }}
                                            {{ order.trip?.driver?.lastName }}
                                        </span>
                                    </div>
                                }

                                @if (order.initialPrice) {
                                    <div
                                        class="flex items-center justify-between"
                                    >
                                        <span
                                            class="text-sm text-text-color-300"
                                            >Initial Price</span
                                        >
                                        <span
                                            class="text-sm font-medium text-text-color-200"
                                            >${{ order.initialPrice }}</span
                                        >
                                    </div>
                                }

                                @if (order.finalPrice) {
                                    <div
                                        class="flex items-center justify-between"
                                    >
                                        <span
                                            class="text-sm text-text-color-300"
                                            >Final Price</span
                                        >
                                        <span
                                            class="text-sm font-semibold text-main-color-600"
                                            >${{
                                                order.finalPrice.toFixed(2)
                                            }}</span
                                        >
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Card Actions -->
                        <div
                            class="border-t border-background-color-300 bg-background-color-100 p-4"
                        >
                            <div class="grid grid-cols-2 gap-2">
                                <!-- View Trip Path Button -->
                                @if (order.pickupPoint && order.dropoffPoint) {
                                    <button
                                        class="flex flex-1 items-center justify-center gap-2 rounded-lg bg-main-color-600 px-4 py-2 text-sm font-medium text-white transition-colors duration-200 hover:bg-main-color-700"
                                        (click)="openTripMapDialog(order)"
                                    >
                                        <i class="pi pi-map text-sm"></i>
                                        View Path
                                    </button>
                                }

                                <!-- Cancel Button (for cancellable orders) -->
                                @if (canCancelOrder(order)) {
                                    <button
                                        class="flex flex-1 items-center justify-center gap-2 rounded-lg bg-red-600 px-4 py-2 text-sm font-medium text-white transition-colors duration-200 hover:bg-red-700"
                                        (click)="cancelOrder(order)"
                                    >
                                        <i class="pi pi-times text-sm"></i>
                                        Cancel
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>
