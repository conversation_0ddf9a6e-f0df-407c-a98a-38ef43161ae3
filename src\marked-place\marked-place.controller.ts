import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { MarkedPlaceService } from './marked-place.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { MarkedPlace, User } from '@prisma/client';
import { GetUser } from '../common/decorators/get-user.decorator';
import { CreateMarkedPlaceDto } from './dto/create-marked-place.dto';
import { UpdateMarkedPlaceDto } from './dto/update-marked-place.dto';

@Controller('api/marked-places')
@UseGuards(AuthGuard)
export class MarkedPlaceController {
  constructor(private readonly markedPlaceService: MarkedPlaceService) {}

  @Get()
  findAll(@GetUser() user: User): Promise<MarkedPlace[]> {
    return this.markedPlaceService.findAll(user.id);
  }

  @Get(':id')
  findOne(
    @Param('id') id: string,
    @GetUser() user: User,
  ): Promise<MarkedPlace> {
    return this.markedPlaceService.findOne(id, user.id);
  }

  @Post()
  create(
    @Body() createMarkedPlaceDto: CreateMarkedPlaceDto,
    @GetUser() user: User,
  ): Promise<MarkedPlace> {
    return this.markedPlaceService.create({
      ...createMarkedPlaceDto,
      userId: user.id,
    });
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateMarkedPlaceDto: UpdateMarkedPlaceDto,
    @GetUser() user: User,
  ): Promise<MarkedPlace> {
    return this.markedPlaceService.update(id, user.id, updateMarkedPlaceDto);
  }

  @Delete(':id')
  delete(@Param('id') id: string, @GetUser() user: User): Promise<MarkedPlace> {
    return this.markedPlaceService.delete(id, user.id);
  }
}
