import { LatLng, LatLngTuple } from 'leaflet';
import { CreateOrderDto } from '../../services';

export const decode = (str: string, precision: number = 6) => {
    let index = 0;
    let lat = 0;
    let lng = 0;
    const coordinates: LatLngTuple[] = [];
    let shift = 0;
    let result = 0;
    let byte = null;
    let latitude_change;
    let longitude_change;

    const factor = Math.pow(10, precision || 6);

    // Coordinates have variable length when encoded, so just keep
    // track of whether we've hit the end of the string. In each
    // loop iteration, a single coordinate is decoded.
    while (index < str.length) {
        // Reset shift, result, and byte
        byte = null;
        shift = 0;
        result = 0;

        do {
            byte = str.charCodeAt(index++) - 63;
            result |= (byte & 0x1f) << shift;
            shift += 5;
        } while (byte >= 0x20);

        latitude_change = result & 1 ? ~(result >> 1) : result >> 1;

        shift = result = 0;

        do {
            byte = str.charCodeAt(index++) - 63;
            result |= (byte & 0x1f) << shift;
            shift += 5;
        } while (byte >= 0x20);

        longitude_change = result & 1 ? ~(result >> 1) : result >> 1;

        lat += latitude_change;
        lng += longitude_change;

        coordinates.push([lat / factor, lng / factor]);
    }

    return coordinates;
};

export const parseDirectionsGeometry = (data: any[]) => {
    const coordinates: LatLngTuple[] = [];

    for (const feat of data) {
        coordinates.push(...decode(feat.shape, 6));
    }

    return coordinates;
};

/**
 * Calculate fallback estimates using straight-line distance
 */
export const calculateFallbackEstimates = (dto: CreateOrderDto) => {
    // Calculate straight-line distance using Haversine formula
    const R = 6371; // Earth's radius in kilometers
    const lat1 = (dto.pickupLatitude * Math.PI) / 180;
    const lat2 = (dto.dropoffLatitude * Math.PI) / 180;
    const deltaLat =
        ((dto.dropoffLatitude - dto.pickupLatitude) * Math.PI) / 180;
    const deltaLon =
        ((dto.dropoffLongitude - dto.pickupLongitude) * Math.PI) / 180;

    const a =
        Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
        Math.cos(lat1) *
            Math.cos(lat2) *
            Math.sin(deltaLon / 2) *
            Math.sin(deltaLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    const straightLineDistance = R * c; // Distance in km

    // Estimate road distance (typically 1.3x straight line in cities)
    const estimatedRoadDistance = straightLineDistance * 1.3;

    // Estimate time based on average city speed (25 km/h with traffic)
    const averageSpeedKmh = 25;
    const estimatedTimeHours = estimatedRoadDistance / averageSpeedKmh;
    const estimatedTimeMinutes = Math.round(estimatedTimeHours * 60);

    return {
        distance: estimatedRoadDistance,
        time: estimatedTimeMinutes,
    };
};

/**
 * Check if two locations are the same or very close (within 100 meters)
 */
export const isSameLocation = (
    location1: LatLng | null,
    location2: LatLng | null,
) => {
    if (!location1 || !location2) return false;

    // Calculate distance between two points using Haversine formula
    const R = 6371000; // Earth's radius in meters
    const lat1 = (location1.lat * Math.PI) / 180;
    const lat2 = (location2.lat * Math.PI) / 180;
    const deltaLat = ((location2.lat - location1.lat) * Math.PI) / 180;
    const deltaLon = ((location2.lng - location1.lng) * Math.PI) / 180;

    const a =
        Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
        Math.cos(lat1) *
            Math.cos(lat2) *
            Math.sin(deltaLon / 2) *
            Math.sin(deltaLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    const distance = R * c; // Distance in meters

    // Consider locations the same if they're within 100 meters
    return distance < 100;
};
