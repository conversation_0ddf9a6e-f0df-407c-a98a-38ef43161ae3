import { CommonModule } from '@angular/common';
import { Component, OnInit, inject, input } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ToastModule } from 'primeng/toast';
import { TripService } from '../../services';
import { Order, OrderService, OrderStatus } from '../../services/order.service';

@Component({
    selector: 'app-my-trips',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        ToastModule,
        ConfirmDialogModule,
    ],
    templateUrl: './my-trips.component.html',
    providers: [DialogService, MessageService, ConfirmationService],
})
export class MyTripsComponent implements OnInit {
    private orderService = inject(OrderService);
    private tripService = inject(TripService);

    confirmationService = inject(ConfirmationService);
    id = input.required<string>();

    order!: Order;
    private dialogRef: DynamicDialogRef | undefined;

    ngOnInit(): void {
        this.loadOrders();
    }

    loadOrders(): void {
        this.tripService.getTripById(this.id()).subscribe((response) => {
            if (response.data) {
                console.log(response.data);
            }
        });
    }

    viewTripDetails(order: Order): void {
        if (order.tripId) {
            this.tripService.getTripById(order.tripId).subscribe((response) => {
                if (response.data) {
                }
            });
        }
    }

    cancelOrder(order: Order): void {
        if (order.status === OrderStatus.PENDING) {
            this.confirmationService.confirm({
                message: 'Are you sure you want to cancel this trip?',
                header: 'Cancel Trip',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    // ToDo;
                    // this.orderService.
                },
            });
        }
    }

    formatDate(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    ngOnDestroy(): void {
        if (this.dialogRef) {
            this.dialogRef.close();
        }
    }
}
