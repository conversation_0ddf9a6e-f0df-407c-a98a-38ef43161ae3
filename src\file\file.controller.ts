// src/files/files.controller.ts
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, UseGuards } from '@nestjs/common';
import { createReadStream } from 'fs';
import { join } from 'path';
import { Response } from 'express';
import { AuthGuard } from 'src/common/guards';

@Controller('/api/files')
export class FilesController {
  @Get('images/:filename')
  @Header('Content-Type', 'image/jpeg')
  @UseGuards(AuthGuard)
  getImage(@Param('filename') filename: string, @Res() res: Response) {
    const file = createReadStream(join(process.cwd(), 'uploads', filename));
    file.pipe(res);
  }
}
