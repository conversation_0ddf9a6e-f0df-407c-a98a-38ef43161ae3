import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { of } from 'rxjs';

import { NearPickupLocationStepComponent } from './near-pickup-location-step.component';
import { NominatimService } from '../../../services/nominatim.service';
import { StopPointService } from '../../../services/stop-point.service';
import { ValhallaService } from '../../../services/valhala.service';
import { MapComponent } from '../../../shared/components/map.component';
import { ScheduledTripData } from '../scheduled-trip-steps.component';
import { latLng } from 'leaflet';

describe('NearPickupLocationStepComponent - Enhanced with Map and Stop Points', () => {
    let component: NearPickupLocationStepComponent;
    let fixture: ComponentFixture<NearPickupLocationStepComponent>;
    let mockNominatimService: jasmine.SpyObj<NominatimService>;
    let mockStopPointService: jasmine.SpyObj<StopPointService>;
    let mockValhallaService: jasmine.SpyObj<ValhallaService>;

    const mockStopPoints = [
        { id: '1', name: 'Central Station', latitude: 40.7128, longitude: -74.0060 },
        { id: '2', name: 'Times Square', latitude: 40.7589, longitude: -73.9851 },
        { id: '3', name: 'Brooklyn Bridge', latitude: 40.7061, longitude: -73.9969 },
    ];

    const mockValhallaResponse = {
        trip: {
            legs: [
                {
                    shape: 'encoded_polyline_string',
                    summary: { length: 2.5, time: 300 }
                }
            ]
        }
    };

    beforeEach(async () => {
        const nominatimSpy = jasmine.createSpyObj('NominatimService', ['reverseGeocode']);
        const stopPointSpy = jasmine.createSpyObj('StopPointService', ['getAllStopPoints']);
        const valhallaSpy = jasmine.createSpyObj('ValhallaService', ['getDirections']);

        await TestBed.configureTestingModule({
            imports: [
                CommonModule,
                ButtonModule,
                CardModule,
                MapComponent,
                NearPickupLocationStepComponent
            ],
            providers: [
                { provide: NominatimService, useValue: nominatimSpy },
                { provide: StopPointService, useValue: stopPointSpy },
                { provide: ValhallaService, useValue: valhallaSpy }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(NearPickupLocationStepComponent);
        component = fixture.componentInstance;
        mockNominatimService = TestBed.inject(NominatimService) as jasmine.SpyObj<NominatimService>;
        mockStopPointService = TestBed.inject(StopPointService) as jasmine.SpyObj<StopPointService>;
        mockValhallaService = TestBed.inject(ValhallaService) as jasmine.SpyObj<ValhallaService>;

        // Setup default mocks
        mockStopPointService.getAllStopPoints.and.returnValue(of({ data: mockStopPoints, error: null }));
        mockNominatimService.reverseGeocode.and.returnValue(of({ display_name: 'Test Address' }));
        mockValhallaService.getDirections.and.returnValue(of(mockValhallaResponse));
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should load all stop points on init', () => {
        component.ngOnInit();
        expect(mockStopPointService.getAllStopPoints).toHaveBeenCalled();
        expect(component.allStopPoints()).toEqual(mockStopPoints);
    });

    it('should display map with pickup, dropoff, and stop points', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851),
            pickupAddress: 'NYC Pickup',
            dropoffAddress: 'NYC Dropoff'
        };

        fixture.componentRef.setInput('tripData', tripData);
        component.allStopPoints.set(mockStopPoints);
        
        const nodes = component.mapNodes();
        
        // Should have pickup marker, dropoff marker, and all stop point markers
        expect(nodes.length).toBeGreaterThanOrEqual(5); // 2 main locations + 3 stop points
    });

    it('should handle stop point selection via click', () => {
        const stopPoint = mockStopPoints[0];
        
        component.selectStopPoint(stopPoint);
        
        expect(component.nearPickupLocation()).toBeTruthy();
        expect(component.nearPickupLocation()!.lat).toBe(stopPoint.latitude);
        expect(component.nearPickupLocation()!.lng).toBe(stopPoint.longitude);
        expect(component.nearPickupAddress()).toBe(stopPoint.name);
        expect(component.nearPickupSelected()).toBe(true);
    });

    it('should create route using Valhalla service', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        fixture.detectChanges();

        expect(mockValhallaService.getDirections).toHaveBeenCalled();
        
        const callArgs = mockValhallaService.getDirections.calls.mostRecent().args[0];
        expect(callArgs.locations).toHaveLength(2);
        expect(callArgs.costing).toBe('auto');
    });

    it('should include near pickup in route when selected', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };
        const nearPickup = latLng(40.7200, -74.0100);

        fixture.componentRef.setInput('tripData', tripData);
        component.nearPickupLocation.set(nearPickup);
        fixture.detectChanges();

        expect(mockValhallaService.getDirections).toHaveBeenCalled();
        
        const callArgs = mockValhallaService.getDirections.calls.mostRecent().args[0];
        expect(callArgs.locations).toHaveLength(3);
        expect(callArgs.locations[0]).toEqual({ lat: nearPickup.lat, lon: nearPickup.lng });
    });

    it('should show map legend with correct colors', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        fixture.detectChanges();

        const compiled = fixture.nativeElement;
        expect(compiled.textContent).toContain('Map Legend');
        expect(compiled.textContent).toContain('Pickup');
        expect(compiled.textContent).toContain('Dropoff');
        expect(compiled.textContent).toContain('Stop Points (Click to Select)');
    });

    it('should emit completed event with selected location', () => {
        const location = latLng(40.7128, -74.0060);
        const address = 'Central Station';
        
        spyOn(component.completed, 'emit');
        
        component.nearPickupLocation.set(location);
        component.nearPickupAddress.set(address);
        
        component.confirmNearPickup();
        
        expect(component.completed.emit).toHaveBeenCalledWith({
            location,
            address
        });
    });

    it('should update route when near pickup changes', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        fixture.detectChanges();

        // Clear previous calls
        mockValhallaService.getDirections.calls.reset();

        // Change near pickup location
        const nearPickup = latLng(40.7200, -74.0100);
        component.nearPickupLocation.set(nearPickup);

        // Should trigger route update
        expect(mockValhallaService.getDirections).toHaveBeenCalled();
    });
});
