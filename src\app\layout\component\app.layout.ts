import { CommonModule } from '@angular/common';
import {
    Component,
    computed,
    inject,
    Renderer2,
    ViewChild,
} from '@angular/core';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { filter, Subscription } from 'rxjs';
import { LayoutService } from '../service/layout.service';
import { AppSidebar } from './app.sidebar';
import { FcmService } from '../../services/fcm.service';
import { Toast } from 'primeng/toast';

@Component({
    selector: 'app-layout',
    standalone: true,
    imports: [CommonModule, AppSidebar, RouterModule, Toast],
    styleUrls: ['./app.layout.scss'],
    template: `<div class="layout-wrapper p-4" [ngClass]="containerClass">
        <p-toast></p-toast>

        <!-- Sidebar toggle button -->
        <button
            class="order-flow-menu-button"
            [class.sidebar-active]="isSidebarActive()"
            (click)="toggleSidebar()"
            [title]="isSidebarActive() ? 'Close Menu' : 'Open Menu'"
        >
            <i class="pi pi-angle-double-right"></i>
        </button>

        <!-- Sidebar mask -->
        <div
            class="order-flow-sidebar-mask"
            [class.active]="isSidebarActive()"
            (click)="closeSidebar()"
        ></div>

        <!-- Sidebar -->
        <div
            class="order-flow-sidebar-wrapper"
            [class.active]="isSidebarActive()"
        >
            <!-- Sidebar header with back button -->
            <div class="sidebar-header">
                <h3 class="sidebar-title">Menu</h3>
                <button
                    class="sidebar-back-button"
                    (click)="closeSidebar()"
                    [title]="'Close Menu'"
                >
                    <i class="pi pi-times"></i>
                </button>
            </div>

            <!-- Sidebar content -->
            <div class="sidebar-content">
                <app-sidebar></app-sidebar>
            </div>
        </div>

        <div class="layout-main-container">
            <div class="layout-main">
                <router-outlet></router-outlet>
            </div>
        </div>
        <div class="layout-mask animate-fadein"></div>
    </div> `,
})
export class AppLayout {
    layoutService = inject(LayoutService);
    fcmService = inject(FcmService);
    renderer = inject(Renderer2);
    router = inject(Router);

    isSidebarActive = computed(() => this.layoutService.isSidebarActive());

    overlayMenuOpenSubscription: Subscription;

    menuOutsideClickListener: any;

    @ViewChild(AppSidebar) appSidebar!: AppSidebar;

    /** Inserted by Angular inject() migration for backwards compatibility */
    constructor(...args: unknown[]);

    constructor() {
        this.overlayMenuOpenSubscription =
            this.layoutService.overlayOpen$.subscribe(() => {
                if (!this.menuOutsideClickListener) {
                    this.menuOutsideClickListener = this.renderer.listen(
                        'document',
                        'click',
                        (event) => {
                            if (this.isOutsideClicked(event)) {
                                this.hideMenu();
                            }
                        },
                    );
                }

                if (this.layoutService.layoutState().staticMenuMobileActive) {
                    this.blockBodyScroll();
                }
            });

        this.router.events
            .pipe(filter((event) => event instanceof NavigationEnd))
            .subscribe(() => {
                this.hideMenu();
            });

        this.fcmService.requestPermission();
    }

    toggleSidebar(): void {
        this.layoutService.onMenuToggle();
    }

    closeSidebar(): void {
        this.hideMenu();
    }
    isOutsideClicked(event: MouseEvent) {
        const sidebarEl = document.querySelector('.layout-sidebar');
        const topbarEl = document.querySelector('.layout-menu-button');
        const eventTarget = event.target as Node;

        return !(
            sidebarEl?.isSameNode(eventTarget) ||
            sidebarEl?.contains(eventTarget) ||
            topbarEl?.isSameNode(eventTarget) ||
            topbarEl?.contains(eventTarget)
        );
    }

    hideMenu() {
        this.layoutService.layoutState.update((prev) => ({
            ...prev,
            overlayMenuActive: false,
            staticMenuMobileActive: false,
            menuHoverActive: false,
        }));
        if (this.menuOutsideClickListener) {
            this.menuOutsideClickListener();
            this.menuOutsideClickListener = null;
        }
        this.unblockBodyScroll();
    }

    blockBodyScroll(): void {
        if (document.body.classList) {
            document.body.classList.add('blocked-scroll');
        } else {
            document.body.className += ' blocked-scroll';
        }
    }

    unblockBodyScroll(): void {
        if (document.body.classList) {
            document.body.classList.remove('blocked-scroll');
        } else {
            document.body.className = document.body.className.replace(
                new RegExp(
                    '(^|\\b)' +
                        'blocked-scroll'.split(' ').join('|') +
                        '(\\b|$)',
                    'gi',
                ),
                ' ',
            );
        }
    }

    get containerClass() {
        return {
            'layout-overlay':
                this.layoutService.layoutConfig().menuMode === 'overlay',
            'layout-static':
                this.layoutService.layoutConfig().menuMode === 'static',
            'layout-static-inactive':
                this.layoutService.layoutState().staticMenuDesktopInactive &&
                this.layoutService.layoutConfig().menuMode === 'static',
            'layout-overlay-active':
                this.layoutService.layoutState().overlayMenuActive,
            'layout-mobile-active':
                this.layoutService.layoutState().staticMenuMobileActive,
        };
    }

    ngOnDestroy() {
        if (this.overlayMenuOpenSubscription) {
            this.overlayMenuOpenSubscription.unsubscribe();
        }

        if (this.menuOutsideClickListener) {
            this.menuOutsideClickListener();
        }
    }
}
