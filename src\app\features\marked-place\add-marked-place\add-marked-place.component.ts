import { Component, computed, input, model, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import {
    FormBuilder,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { divIcon, latLng, LatLng, Layer, MapOptions, marker } from 'leaflet';
import { AutoComplete } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { ColorPickerModule } from 'primeng/colorpicker';
import { InputTextModule } from 'primeng/inputtext';
import { Tooltip } from 'primeng/tooltip';
import { debounceTime, distinct, filter, map, switchMap } from 'rxjs';
import { MarkedPlaceService } from '../../../services';
import { NominatimService } from '../../../services/nominatim.service';
import { MapComponent } from '../../../shared/components/map.component';
import { injectMany } from '../../../shared/helpers/injectMany';
import { getlocation } from '../../../shared/helpers/location';

@Component({
    selector: 'app-add-marked-place',
    standalone: true,
    imports: [
        FormsModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        ColorPickerModule,
        MapComponent,
        RouterLink,
        AutoComplete,
        Tooltip,
    ],
    templateUrl: './add-marked-place.component.html',
})
export class AddMarkedPlaceComponent {
    services = injectMany({
        FormBuilder,
        MarkedPlaceService,
        Router,
        NominatimService,
    });

    id = input.required<string>();

    marked = computed(() => {
        this.services.MarkedPlaceService.getMarkedPlaceById(
            this.id(),
        ).subscribe((val) => {
            if (val.data) {
                this.placeForm.setValue(val.data);
                this.selectedLocation.set(
                    latLng(val.data.latitude, val.data.longitude),
                );
            }
            return val.data;
        });
    });

    options = signal<MapOptions>({});

    search = model<string>('');
    searchObs = toObservable(this.search).pipe(
        filter((val) => !!val),
        distinct(),
        debounceTime(2000),
        switchMap((val: string) => {
            return this.services.NominatimService.forwardGeocode(val).pipe(
                map((val) => {
                    return val.map((item) => {
                        return {
                            display_name: item.display_name,
                            latlng: [item.lat, item.lon],
                        };
                    });
                }),
            );
        }),
    );
    searchRes$ = toSignal(this.searchObs);

    placeForm: FormGroup = this.services.FormBuilder.group({
        name: ['', Validators.required],
        // color: [getRandomColor(), Validators.required],
    });
    selectedLocation = signal<LatLng | null>(null);
    mapMarker = signal<Layer[]>([]);

    ngOnInit() {}

    handleMapClick(event: { latlng: LatLng }) {
        this.selectedLocation.set(event.latlng);

        const newMarker = marker(event.latlng, {
            draggable: false,
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div class="flex size-10 items-center justify-center rounded-full" style="background-color: rgba(0, 0, 0, 0.3); border: 2px solid #000000;">
                <div class="flex size-8 items-center justify-center rounded-full" style="background-color: #000000; color: #ffffff;">
                </div>
              </div>`,
            }),
        });

        this.mapMarker.set([newMarker]);
    }

    pointGeoLocation() {
        getlocation().subscribe((data) => {
            this.addMarkerOnClick({
                latlng: latLng(data.latitude, data.longitude),
            });
        });
    }
    addMarkerOnClick(event: { latlng: LatLng }) {
        const clickedLatLng = event.latlng;
        const newMarker = marker(clickedLatLng, {
            draggable: true,
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div class="flex size-10 items-center justify-center rounded-full" style="background-color: rgba(0, 0, 0, 0.3); border: 2px solid #000000;">
                          <div class="flex size-8 items-center justify-center rounded-full" style="background-color: #000000; color: #ffffff;">
                          </div>
                      </div>`,
            }),
        })
            .on('dragend', () => {
                const r = this.mapMarker();
                this.mapMarker.set([]);
                this.mapMarker.set(r);
            })
            .on('click', () => {});

        this.mapMarker.set([newMarker]);
    }
    afterSearch(event: any) {
        this.selectedLocation.set(latLng(event.latlng));
        this.addMarkerOnClick(event);
        this.options.set({ center: event.latlng, zoom: 12 });
        this.search.set('');
    }

    savePlace() {
        const location = this.selectedLocation();
        if (this.placeForm.valid && location) {
            const newPlace = {
                name: this.placeForm.get('name')?.value,
                color: this.placeForm.get('color')?.value,
                latitude: location.lat,
                longitude: location.lng,
            };

            let req =
                this.services.MarkedPlaceService.createMarkedPlace(newPlace);
            if (this.id()) {
                req = this.services.MarkedPlaceService.updateMarkedPlace(
                    this.id(),
                    newPlace,
                );
            }
            req.subscribe(() => {
                this.services.Router.navigate(['/main', 'marked-places']);
            });
        }
    }
}
