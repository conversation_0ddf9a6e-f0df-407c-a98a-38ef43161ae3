import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../../services/user.service';
import { DriverStatus } from '../../core/types/tripoos.types';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-role-redirect',
    standalone: true,
    imports: [CommonModule],
    template: `
        <div class="flex h-screen items-center justify-center">
            <div class="text-center">
                <div class="mb-4">
                    <i class="pi pi-spin pi-spinner text-4xl text-primary"></i>
                </div>
                <p class="text-lg text-muted-color">Redirecting...</p>
            </div>
        </div>
    `,
})
export class RoleRedirectComponent implements OnInit {
    private userService = inject(UserService);
    private router = inject(Router);

    ngOnInit() {
        this.redirectBasedOnRole();
    }

    private redirectBasedOnRole() {
        this.userService.getProfile().subscribe({
            next: (response) => {
                if (response.data) {
                    // Check if user is an approved driver
                    const isApprovedDriver = response.data.driverStatus === DriverStatus.APPROVED;
                    
                    if (isApprovedDriver) {
                        // Redirect drivers to find orders page
                        this.router.navigate(['/main/find-orders']);
                    } else {
                        // Redirect regular users to order booking page
                        this.router.navigate(['/main/order']);
                    }
                } else {
                    // Fallback to order page if no user data
                    this.router.navigate(['/main/order']);
                }
            },
            error: (error) => {
                console.error('Error loading user profile:', error);
                // Fallback to order page on error
                this.router.navigate(['/main/order']);
            }
        });
    }
} 