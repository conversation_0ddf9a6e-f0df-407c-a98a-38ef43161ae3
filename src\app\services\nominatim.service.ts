import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import {
    GeocodingResult,
    ReverseGeocodingResult,
} from '../shared/types/nominatim.types';

@Injectable({
    providedIn: 'root',
})
export class NominatimService {
    private readonly NOMINATIM_URL = `${environment.nominatimUrl}/search`;
    private readonly NOMINATIM_REVERSE_URL = `${environment.nominatimUrl}/reverse`;

    constructor(private http: HttpClient) {}

    /**
     * Forward geocoding - converts address to coordinates
     * @param userInput - The address or place name to search for
     * @returns An observable of geocoding results
     */
    forwardGeocode(userInput: string): Observable<GeocodingResult[]> {
        const params = new HttpParams()
            .set('q', userInput)
            .set('format', 'json')
            .set('limit', '5');

        const headers = new HttpHeaders().set('accept-language', 'ar-sa');
        return this.http.get<GeocodingResult[]>(this.NOMINATIM_URL, {
            params,
            headers,
        });
    }

    /**
     * Reverse geocoding - converts coordinates to address
     * @param lon - Longitude coordinate
     * @param lat - Latitude coordinate
     * @returns An observable of reverse geocoding result
     */
    reverseGeocode(
        lon: number,
        lat: number,
    ): Observable<ReverseGeocodingResult> {
        const params = new HttpParams()
            .set('lon', lon.toString())
            .set('lat', lat.toString())
            .set('format', 'json');

        const headers = new HttpHeaders().set('accept-language', 'ar-sa');

        return this.http.get<ReverseGeocodingResult>(
            this.NOMINATIM_REVERSE_URL,
            { params, headers },
        );
    }
}
