import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/common/prisma/prisma.service';
import { User } from '@prisma/client';

describe('MarkedPlace (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let authToken: string;
  let testUser: User;

  beforeAll(async () => {
    try {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      app = moduleFixture.createNestApplication();
      app.useGlobalPipes(new ValidationPipe());
      prisma = app.get<PrismaService>(PrismaService);
      await app.init();

      // Clean up ALL related tables in the correct order
      await prisma.$transaction([
        prisma.order.deleteMany(),
        prisma.trip.deleteMany(),
        prisma.markedPlace.deleteMany(),
        prisma.stopPoint.deleteMany(),
        prisma.refreshToken.deleteMany(),
        prisma.user.deleteMany(),
      ]);

      // First sign up the user
      const signUpResponse = await request(app.getHttpServer())
        .post('/api/users/auth/sign-up')
        .send({
          first_name: 'Test',
          last_name: 'User',
          phone_number: '+**********',
          password: 'password123'
        });

      // Verify the phone number
      await request(app.getHttpServer())
        .post('/api/users/auth/verify-phone')
        .send({
          phone_number: '+**********',
          code: signUpResponse.body.verificationCode
        });

      // Now sign in
      const signInResponse = await request(app.getHttpServer())
        .post('/api/users/auth/sign-in')
        .send({
          phone_number: '+**********',
          password: 'password123'
        });

      if (!signInResponse.body.access_token) {
        throw new Error(`Sign in failed: ${JSON.stringify(signInResponse.body)}`);
      }

      authToken = signInResponse.body.access_token;
      
      // Get the user details
      testUser = await prisma.user.findUnique({
        where: { phoneNumber: '+**********' }
      });

    } catch (error) {
      console.error('Setup failed:', error);
      throw error;
    }
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.markedPlace.deleteMany();
    await prisma.refreshToken.deleteMany();
  });

  afterAll(async () => {
    // Delete in correct order to handle foreign key constraints
    await prisma.markedPlace.deleteMany();
    await prisma.refreshToken.deleteMany();
    await prisma.user.deleteMany();
    await prisma.$disconnect();
    await app.close();
  });

  describe('POST /api/marked-places', () => {
    it('should create a marked place', () => {
      return request(app.getHttpServer())
        .post('/api/marked-places')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Home',
          latitude: 51.5074,
          longitude: -0.1278
        })
        .expect(201)
        .expect(({ body }) => {
          expect(body.name).toBe('Home');
          expect(body.latitude).toBe(51.5074);
          expect(body.longitude).toBe(-0.1278);
          expect(body.userId).toBe(testUser.id);
        });
    });

    it('should fail without auth token', () => {
      return request(app.getHttpServer())
        .post('/api/marked-places')
        .send({
          name: 'Home',
          latitude: 51.5074,
          longitude: -0.1278
        })
        .expect(401);
    });
  });

  describe('GET /api/marked-places', () => {
    beforeEach(async () => {
      await prisma.markedPlace.createMany({
        data: [
          {
            name: 'Home',
            latitude: 51.5074,
            longitude: -0.1278,
            userId: testUser.id
          },
          {
            name: 'Work',
            latitude: 51.5074,
            longitude: -0.1278,
            userId: testUser.id
          }
        ]
      });
    });

    it('should return all marked places for user', () => {
      return request(app.getHttpServer())
        .get('/api/marked-places')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect(({ body }) => {
          expect(body).toHaveLength(2);
          expect(body[0].name).toBe('Home');
          expect(body[1].name).toBe('Work');
        });
    });
  });

  describe('GET /api/marked-places/:id', () => {
    let markedPlace;

    beforeEach(async () => {
      markedPlace = await prisma.markedPlace.create({
        data: {
          name: 'Home',
          latitude: 51.5074,
          longitude: -0.1278,
          userId: testUser.id
        }
      });
    });

    it('should return a specific marked place', () => {
      return request(app.getHttpServer())
        .get(`/api/marked-places/${markedPlace.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect(({ body }) => {
          expect(body.id).toBe(markedPlace.id);
          expect(body.name).toBe('Home');
        });
    });

    it('should return 404 for non-existent marked place', () => {
      return request(app.getHttpServer())
        .get('/api/marked-places/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
