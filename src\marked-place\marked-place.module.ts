import { Modu<PERSON> } from '@nestjs/common';
import { MarkedPlaceController } from './marked-place.controller';
import { MarkedPlaceService } from './marked-place.service';
import { JwtModule } from '@nestjs/jwt';
import { PrismaModule } from '../common/prisma/prisma.module';
import { jwtConfig } from '../common/config/jwt.config';

@Module({
  imports: [PrismaModule, JwtModule.registerAsync(jwtConfig)],
  controllers: [MarkedPlaceController],
  providers: [MarkedPlaceService],
})
export class MarkedPlaceModule {}
