// ================================================================================
// APP LAYOUT COMPONENT STYLES
// ================================================================================

// --------------------------------
// 1. HAMBURGER MENU BUTTON
// --------------------------------

.order-flow-menu-button {
    position: fixed;
    top: 1rem;
    left: 0;
    z-index: 1001;
    width: 3.5rem;
    height: 3.5rem;
    background-color: black;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    box-shadow: 2px 2px 12px rgba(0, 140, 69, 0.3);
    border-radius: 0 1rem 1rem 0;
    transform: translateX(-0.5rem);

    &:hover {
        background-color: rgb(69, 69, 69);
        transform: translateX(0);
        box-shadow: 4px 4px 20px rgba(0, 140, 69, 0.4);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 140, 69, 0.5);
    }

    &:active {
        transform: translateX(0.2rem);
        background-color: #006030;
    }

    i {
        position: relative;
        transition: transform 0.3s ease;
    }

    &:hover i {
        transform: translateX(2px);
    }

    // When sidebar is active, change icon and position
    &.sidebar-active {
        background-color: #dc3545;
        
        &:hover {
            background-color: #c82333;
        }
        
        i {
            transform: rotate(180deg);
        }
        
        &:hover i {
            transform: rotate(180deg) translateX(-2px);
        }
    }

    @media (max-width: 768px) {
        width: 3rem;
        height: 3rem;
        font-size: 1.125rem;
    }
}

// --------------------------------
// 2. SIDEBAR WRAPPER
// --------------------------------

.order-flow-sidebar-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 20rem;
    height: 100vh;
    z-index: 1002;
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.05, 0.74, 0.2, 0.99);
    background-color: white;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    
    &.active {
        transform: translateX(0);
    }
}

// --------------------------------
// 3. SIDEBAR HEADER WITH BACK BUTTON
// --------------------------------

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    min-height: 4rem;

    .sidebar-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin: 0;
    }

    .sidebar-back-button {
        background: none;
        border: none;
        color: #6c757d;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;

        &:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        &:active {
            background-color: #dee2e6;
        }
    }
}

// --------------------------------
// 4. SIDEBAR CONTENT
// --------------------------------

.sidebar-content {
    height: calc(100vh - 4rem);
    overflow-y: auto;
    overflow-x: hidden;
}

// --------------------------------
// 5. SIDEBAR MASK
// --------------------------------

.order-flow-sidebar-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1001;
    background-color: rgba(0, 0, 0, 0.4);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    
    &.active {
        opacity: 1;
        visibility: visible;
    }
}

// --------------------------------
// 6. GLOBAL LAYOUT OVERRIDES FOR ORDER FLOW
// --------------------------------

// Ensure sidebar appears above order-steps component
:global(.layout-sidebar) {
    z-index: 1002 !important;
}

// Global override for full-width layout when order steps component is present
:global(.layout-main-container) {
    &.order-flow-active {
        padding: 0 !important;
        margin: 0 !important;
        min-height: 100vh !important;
        height: 100vh !important;
        overflow: hidden !important;

        .layout-main {
            height: 100vh !important;
            overflow: hidden !important;
            display: flex !important;
            flex-direction: column !important;
        }
    }
}

// Global body scroll prevention when order flow is active
:global(html.order-flow-active),
:global(body.order-flow-active) {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

// Prevent scrolling on all parent containers
:global(.layout-wrapper.order-flow-active) {
    overflow: hidden !important;
    height: 100vh !important;
    position: relative !important;
}

// Additional override for any PrimeNG or other global elements
:global(.order-flow-active) {
    * {
        &:not(.leaflet-container):not(.leaflet-control-container):not([class*="leaflet"]) {
            overflow-x: hidden !important;
        }
    }
}

// --------------------------------
// 7. RESPONSIVE ADJUSTMENTS
// --------------------------------

@media (max-width: 768px) {
    .order-flow-sidebar-wrapper {
        width: 18rem;
    }
}

@media (max-width: 480px) {
    .order-flow-sidebar-wrapper {
        width: 16rem;
    }

    .sidebar-header {
        padding: 0.75rem 1rem;
        min-height: 3.5rem;

        .sidebar-title {
            font-size: 1.125rem;
        }

        .sidebar-back-button {
            width: 2rem;
            height: 2rem;
            font-size: 1.25rem;
        }
    }
}
