import { Injectable, BadRequestException } from '@nestjs/common';
import { DriverStatus, User } from '@prisma/client';
import * as fs from 'fs';
import { CreateDriverDto } from './dto/createDriverDto.dto';
import { PrismaService } from 'src/common/prisma/prisma.service';

@Injectable()
export class DriverService {
  constructor(private prisma: PrismaService) {}

  async processDriverUpload(
    files: {
      idFront?: Express.Multer.File[];
      idBack?: Express.Multer.File[];
      personalPhoto?: Express.Multer.File[];
      carPhotos?: Express.Multer.File[];
    },
    createDriverDto: CreateDriverDto,
    user: User,
  ) {
    if (user.driverStatus !== DriverStatus.NONE) {
      throw new BadRequestException(
        `User already has driver status: ${user.driverStatus}`,
      );
    }

    const uploadedFiles = [
      ...(files.idFront || []),
      ...(files.idBack || []),
      ...(files.personalPhoto || []),
      ...(files.carPhotos || []),
    ];

    try {
      return await this.prisma.$transaction(async (tx) => {
        // Update user
        const updatedUser = await tx.user.update({
          where: { phoneNumber: user.phoneNumber },
          data: {
            IdCardFrontUrl: files.idFront?.[0]?.filename,
            IdCardBackUrl: files.idBack?.[0]?.filename,
            PersonalPhotoUrl: files.personalPhoto?.[0]?.filename,
            driverStatus: DriverStatus.IN_REVIEW,
          },
        });

        // Upsert car
        const car = await tx.car.upsert({
          where: { userId: user.id },
          update: {
            make: createDriverDto.make,
            model: createDriverDto.model,
            year: createDriverDto.year,
            licensePlate: createDriverDto.licensePlate,
          },
          create: {
            make: createDriverDto.make,
            model: createDriverDto.model,
            year: createDriverDto.year,
            licensePlate: createDriverDto.licensePlate,
            userId: user.id,
          },
        });

        // Create car photos
        if (files.carPhotos?.length) {
          await tx.carPhoto.createMany({
            data: files.carPhotos.map((photo) => ({
              carId: car.id,
              photoUrl: photo.filename,
            })),
          });
        }

        return { status: updatedUser.driverStatus };
      });
    } catch (error) {
      // Cleanup files on error
      uploadedFiles.forEach((file) => {
        fs.unlink(file.path, () => {});
      });
      throw error;
    }
  }

  async updateDriverStatus(
    userId: string,
    currentStatus: DriverStatus,
    newStatus: DriverStatus,
  ) {
    return this.prisma.$transaction(async (tx) => {
      // Verify current status
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: { driverStatus: true },
      });

      if (user.driverStatus !== currentStatus) {
        throw new BadRequestException(
          `Current status is ${user.driverStatus}, expected ${currentStatus}`,
        );
      }

      // Update status
      return tx.user.update({
        where: { id: userId },
        data: { driverStatus: newStatus },
        select: { driverStatus: true },
      });
    });
  }
}
