import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TripController } from './trip.controller';
import { TripService } from './trip.service';
import { JwtModule } from '@nestjs/jwt';
import { PrismaModule } from '../common/prisma/prisma.module';
import { jwtConfig } from '../common/config/jwt.config';

@Module({
  imports: [PrismaModule, JwtModule.registerAsync(jwtConfig)],
  controllers: [TripController],
  providers: [TripService],
  exports: [TripService],
})
export class TripModule {}
