/** @type {import('tailwindcss').Config} */
import PrimeUI from 'tailwindcss-primeui';

export default {
    darkMode: ['selector', '[class="app-dark"]'],
    content: ['./src/**/*.{html,ts,scss,css}', './index.html'],
    plugins: [PrimeUI],
    theme: {
        extend: {
            colors: {
                // Background colors
                'background-color-100': 'var(--background-color-100)',
                'background-color-200': 'var(--background-color-200)',
                'background-color-300': 'var(--background-color-300)',
                'background-color-350': 'var(--background-color-350)',
                'background-color-400': 'var(--background-color-400)',
                'background-color-500': 'var(--background-color-500)',
                'background-color-600': 'var(--background-color-600)',
                'background-color-650': 'var(--background-color-650)',
                'background-color-700': 'var(--background-color-700)',
                'background-color-750': 'var(--background-color-750)',
                'background-color-800': 'var(--background-color-800)',
                'background-color-900': 'var(--background-color-900)',
                'background-color-950': 'var(--background-color-950)',
                'background-color-1000': 'var(--background-color-1000)',
                'background-color-1100': 'var(--background-color-1100)',

                // Text colors
                'text-color-100': 'var(--text-color-100)',
                'text-color-200': 'var(--text-color-200)',
                'text-color-300': 'var(--text-color-300)',
                'text-color-400': 'var(--text-color-400)',
                'text-color-500': 'var(--text-color-500)',
                'text-color-600': 'var(--text-color-600)',

                // Border colors
                'border-color-100': 'var(--border-color-100)',
                'border-color-200': 'var(--border-color-200)',
                'border-color-300': 'var(--border-color-300)',
                'border-color-400': 'var(--border-color-400)',
                'border-color-500': 'var(--border-color-500)',
                'border-color-600': 'var(--border-color-600)',

                // Main colors
                'main-color-100': 'var(--main-color-100)',
                'main-color-200': 'var(--main-color-200)',
                'main-color-300': 'var(--main-color-300)',
                'main-color-400': 'var(--main-color-400)',
                'main-color-500': 'var(--main-color-500)',
                'main-color-600': 'var(--main-color-600)',
                'main-color-700': 'var(--main-color-700)',
                'main-color-800': 'var(--main-color-800)',
                'main-color-900': 'var(--main-color-900)',
                'main-color-950': 'var(--main-color-950)',
                'main-color-1000': 'var(--main-color-1000)',

                // Primary brand colors
                'primary-brand-color-100': 'var(--primary-brand-color-100)',
                'primary-brand-color-200': 'var(--primary-brand-color-200)',
                'primary-brand-color-300': 'var(--primary-brand-color-300)',
                'primary-brand-color-400': 'var(--primary-brand-color-400)',

                // Secondary brand colors
                'secondary-brand-color-100': 'var(--secondary-brand-color-100)',
                'secondary-brand-color-200': 'var(--secondary-brand-color-200)',
                'secondary-brand-color-300': 'var(--secondary-brand-color-300)',
                'secondary-brand-color-400': 'var(--secondary-brand-color-400)',
                'secondary-brand-color-500': 'var(--secondary-brand-color-500)',

                // Green colors
                'green-color-100': 'var(--green-color-100)',
                'green-color-200': 'var(--green-color-200)',
                'green-color-300': 'var(--green-color-300)',
                // Orange colors
                'orange-color-100': 'var(--orange-color-100)',
                'orange-color-200': 'var(--orange-color-200)',
                'orange-color-300': 'var(--orange-color-300)',
                'orange-color-400': 'var(--orange-color-400)',
                'orange-color-500': 'var(--orange-color-500)',
                'orange-color-600': 'var(--orange-color-600)',
                'orange-color-700': 'var(--orange-color-700)',

                // Red colors
                'red-color-100': 'var(--red-color-100)',
                'red-color-200': 'var(--red-color-200)',
                'red-color-300': 'var(--red-color-300)',
                'red-color-400': 'var(--red-color-400)',
                'red-color-500': 'var(--red-color-500)',

                // Pink colors
                'pink-color-100': 'var(--pink-color-100)',

                // Gradient
                'gradient-primary': 'var(--linear-gradient)',
            },
            boxShadow: {
                'shadow-100': 'var(--shadow-100)',
                'shadow-200': 'var(--shadow-200)',
                'shadow-300': 'var(--shadow-300)',
                'shadow-400': 'var(--shadow-400)',
                'shadow-500': 'var(--shadow-500)',
                'shadow-600': 'var(--shadow-600)',
                'shadow-700': 'var(--shadow-700)',
            },
        },
        screens: {
            sm: '576px',
            md: '768px',
            lg: '992px',
            xl: '1200px',
            '2xl': '1920px'
        }
    }
};
