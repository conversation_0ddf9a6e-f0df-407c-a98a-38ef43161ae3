import { LatLng } from 'leaflet';

export interface RouteWaypoint {
    id: string;
    location: LatLng;
    address?: string;
    isPickup?: boolean;
    isDropoff?: boolean;
}

export interface RouteInfo {
    waypoints: RouteWaypoint[];
    totalDistance?: number; // in kilometers
    totalDuration?: number; // in minutes
    polyline?: string;
    legs?: Array<{
        distance: number;
        duration: number;
        polyline: string;
    }>;
}
