-- AlterTable
ALTER TABLE "Transaction" ADD COLUMN     "collaborativeTripId" TEXT;

-- CreateTable
CREATE TABLE "CollaborativeTripOffer" (
    "id" TEXT NOT NULL,
    "driverId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "scheduledTime" TIMESTAMP(3) NOT NULL,
    "startpoint" TEXT NOT NULL,
    "endpoint" TEXT NOT NULL,
    "routePoints" JSONB NOT NULL,
    "totalTime" DOUBLE PRECISION NOT NULL,
    "availableSeats" INTEGER NOT NULL,
    "pricePerSeat" DOUBLE PRECISION NOT NULL,
    "nearbyStopPoints" JSONB NOT NULL,
    "possiblePickupPoints" JSONB NOT NULL,
    "possibleDropoffPoints" JSONB NOT NULL,
    "driverNotes" TEXT,

    CONSTRAINT "CollaborativeTripOffer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_CollaborativeTripWaypoints" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_CollaborativeTripWaypoints_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "CollaborativeTripOffer_driverId_idx" ON "CollaborativeTripOffer"("driverId");

-- CreateIndex
CREATE INDEX "CollaborativeTripOffer_scheduledTime_idx" ON "CollaborativeTripOffer"("scheduledTime");

-- CreateIndex
CREATE INDEX "CollaborativeTripOffer_startpoint_idx" ON "CollaborativeTripOffer"("startpoint");

-- CreateIndex
CREATE INDEX "CollaborativeTripOffer_endpoint_idx" ON "CollaborativeTripOffer"("endpoint");

-- CreateIndex
CREATE INDEX "_CollaborativeTripWaypoints_B_index" ON "_CollaborativeTripWaypoints"("B");

-- CreateIndex
CREATE INDEX "Transaction_collaborativeTripId_idx" ON "Transaction"("collaborativeTripId");

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_collaborativeTripId_fkey" FOREIGN KEY ("collaborativeTripId") REFERENCES "CollaborativeTripOffer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CollaborativeTripOffer" ADD CONSTRAINT "CollaborativeTripOffer_driverId_fkey" FOREIGN KEY ("driverId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CollaborativeTripOffer" ADD CONSTRAINT "CollaborativeTripOffer_startpoint_fkey" FOREIGN KEY ("startpoint") REFERENCES "Point"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CollaborativeTripOffer" ADD CONSTRAINT "CollaborativeTripOffer_endpoint_fkey" FOREIGN KEY ("endpoint") REFERENCES "Point"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CollaborativeTripWaypoints" ADD CONSTRAINT "_CollaborativeTripWaypoints_A_fkey" FOREIGN KEY ("A") REFERENCES "CollaborativeTripOffer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CollaborativeTripWaypoints" ADD CONSTRAINT "_CollaborativeTripWaypoints_B_fkey" FOREIGN KEY ("B") REFERENCES "Point"("id") ON DELETE CASCADE ON UPDATE CASCADE;
