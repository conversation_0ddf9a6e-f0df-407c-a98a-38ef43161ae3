import { Injectable } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { MoneyTransaction, TransactionType, TransactionStatus } from '@prisma/client';

@Injectable()
export class TransactionService {
  constructor(private prisma: PrismaService) {}

  async getUserTransactions(userId: string): Promise<MoneyTransaction[]> {
    return this.prisma.moneyTransaction.findMany({
      where: {
        OR: [{ fromUserId: userId }, { toUserId: userId }],
      },
      include: {
        order: {
          include: {
            pickupPoint: true,
            dropoffPoint: true,
          },
        },
        fromUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
          },
        },
        toUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getDriverTransactions(driverId: string): Promise<MoneyTransaction[]> {
    return this.prisma.moneyTransaction.findMany({
      where: {
        OR: [{ fromUserId: driverId }, { toUserId: driverId }],
      },
      include: {
        order: {
          include: {
            pickupPoint: true,
            dropoffPoint: true,
          },
        },
        fromUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
          },
        },
        toUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async markDriverToCompanyTransactionsAsCompleted(driverId: string): Promise<{ updatedCount: number }> {
    const result = await this.prisma.moneyTransaction.updateMany({
      where: {
        fromUserId: driverId,
        type: TransactionType.DRIVER_TO_COMPANY,
        status: TransactionStatus.PENDING,
      },
      data: {
        status: TransactionStatus.COMPLETED,
        completedAt: new Date(),
      },
    });

    return { updatedCount: result.count };
  }
}
