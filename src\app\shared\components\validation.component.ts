import { AsyncPipe } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { AbstractControl, FormControl, FormGroup } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { map, switchMap } from 'rxjs';

@Component({
    selector: 'app-validation',
    template: `{{ errorText() || '' | translate }}`,
    imports: [TranslatePipe],
    standalone: true,
})
export class ValidationComponent {
    errors = input.required<FormControl | AbstractControl>();

    customErrors = input<{ [key: string]: string }>();

    list: { [key: string]: string } = {
        required: 'Validation_required',
        email: 'Validation_email',
    };
    errorsList$ = computed<{ [key: string]: string }>(() => {
        const e = this.customErrors() as { [key: string]: string };
        return { ...this.list, ...e };
    });

    errors$ = toObservable(this.errors);

    errorText = toSignal<string>(
        this.errors$.pipe(
            switchMap((c) => c.events),
            map((v) => {
                let { touched, errors } = this.errors();
                errors = errors ?? {};
                if (touched) {
                    const code = Object.keys(this.errorsList$()).find((val) => {
                        return errors[val] == true;
                    }) as string;
                    return this.errorsList$()[code];
                } else {
                    return ' ';
                }
            }),
        ),
    );

    ngOnInit(): void {}
}
