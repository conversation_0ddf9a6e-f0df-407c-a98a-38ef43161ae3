import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  theme = new BehaviorSubject<string>(this.getSavedTheme());

  theme$ = this.theme.asObservable();

  toggleTheme() {
    const newTheme = this.theme.value === 'dark' ? 'light' : 'dark';
    this.theme.next(newTheme);
    this.applyTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  }

  private applyTheme(theme: string) {
    document.documentElement.setAttribute('data-theme', theme);
  }

  private getSavedTheme(): string {
    return localStorage.getItem('theme') || 'light';
  }

  initializeTheme(){
      this.applyTheme(this.theme.value);
  }
}
