import { CommonModule } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';

import { ConfirmationService, MessageService } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { interval } from 'rxjs';
import { TripService } from '../../services';
import { Order, OrderService, OrderStatus } from '../../services/order.service';
import { TripMapDialogComponent } from '../../shared/components/trip-map-dialog/trip-map-dialog.component';

@Component({
    selector: 'app-trips-history',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        ToastModule,
        ConfirmDialogModule,
    ],
    templateUrl: './trips-history.component.html',
})
export class TripsHistoryComponent implements OnInit, OnDestroy {
    tripService = inject(TripService);
    private orderService = inject(OrderService);
    private dialogService = inject(DialogService);
    private messageService = inject(MessageService);
    private confirmationService = inject(ConfirmationService);
    private router = inject(Router);

    orders: Order[] = [];
    private dialogRef: DynamicDialogRef | undefined;

    ngOnInit(): void {
        this.loadOrders();
    }

    loadOrders(): void {
        this.orderService.getUserOrders().subscribe((response) => {
            if (response.data) {
                this.orders = response.data;
            }
        });
    }

    formatDate(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    /**
     * Get CSS class for order status styling
     * @param status Order status
     * @returns CSS class string
     */
    getStatusClass(status: OrderStatus): string {
        switch (status) {
            case OrderStatus.PENDING:
                return 'bg-blue-100 text-blue-800';
            case OrderStatus.SUGGESTED_FOR_DRIVER:
                return 'bg-yellow-100 text-yellow-800';
            case OrderStatus.CONFIRMED:
                return 'bg-green-100 text-green-800';
            case OrderStatus.COMPLETED:
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Get human-readable status label
     * @param status Order status
     * @returns Human-readable status string
     */
    getStatusLabel(status: OrderStatus): string {
        switch (status) {
            case OrderStatus.PENDING:
                return 'Pending';
            case OrderStatus.SUGGESTED_FOR_DRIVER:
                return 'Finding Driver';
            case OrderStatus.CONFIRMED:
                return 'Confirmed';
            case OrderStatus.COMPLETED:
                return 'Completed';
            default:
                return status;
        }
    }

    /**
     * Check if an order can be cancelled
     * @param order The order to check
     * @returns True if the order can be cancelled
     */
    canCancelOrder(order: Order): boolean {
        return (
            order.status === OrderStatus.PENDING ||
            order.status === OrderStatus.SUGGESTED_FOR_DRIVER
        );
    }

    /**
     * Open trip map dialog to show pickup and dropoff locations
     * @param order The order to show on map
     */
    openTripMapDialog(order: Order): void {
        if (!order.pickupPoint || !order.dropoffPoint) {
            this.messageService.add({
                severity: 'warn',
                summary: 'Location Data Missing',
                detail: 'Pickup or dropoff location data is not available for this trip.',
            });
            return;
        }

        this.dialogRef = this.dialogService.open(TripMapDialogComponent, {
            header: `Trip #${order.id.substring(0, 8)} - Route`,
            width: '90vw',
            height: '80vh',
            maximizable: true,
            closable: true,
            data: {
                trip: {
                    pickupPoint: {
                        latitude: order.pickupPoint.latitude,
                        longitude: order.pickupPoint.longitude,
                    },
                    dropoffPoint: {
                        latitude: order.dropoffPoint.latitude,
                        longitude: order.dropoffPoint.longitude,
                    },
                },
            },
        });
    }

    /**
     * Cancel an order
     * @param order The order to cancel
     */
    cancelOrder(order: Order): void {
        this.confirmationService.confirm({
            message: `Are you sure you want to cancel trip #${order.id.substring(0, 8)}?`,
            header: 'Cancel Trip',
            icon: 'pi pi-exclamation-triangle',
            acceptButtonStyleClass: 'p-button-danger',
            accept: () => {
                this.orderService.cancelOrder(order.id).subscribe(() => {
                    this.loadOrders();
                });
            },
        });
    }

    /**
     * Approve an order (for drivers)
     * @param order The order to approve
     */
    approve(order: Order): void {
        // Check if the order can be approved
        if (
            order.status !== OrderStatus.PENDING &&
            order.status !== OrderStatus.SUGGESTED_FOR_DRIVER
        ) {
            this.messageService.add({
                severity: 'warn',
                summary: 'Cannot Approve',
                detail: 'This order cannot be approved in its current status.',
            });
            return;
        }

        this.orderService.approveOrder(order.id).subscribe({
            next: (response) => {
                if (response.data) {
                    // Check if the response already contains the tripId
                    if (response.data.tripId) {
                        // Navigate immediately to trip info page
                        this.router.navigate([
                            '/main/trip',
                            response.data.tripId,
                        ]);
                    } else {
                        // Fallback: Start polling for trip ID if not immediately available
                        this.pollForTripId(order.id);
                    }
                }
            },
            error: (error) => {
                console.error('Error approving order:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Approval Failed',
                    detail: 'Failed to approve the trip. Please try again.',
                });
            },
        });
    }

    private pollForTripId(orderId: string): void {
        let pollCount = 0;
        const maxPolls = 10; // Maximum 30 seconds of polling (3 seconds * 10)

        const pollSubscription = interval(3000).subscribe(() => {
            pollCount++;

            this.orderService.getUserOrders().subscribe({
                next: (response) => {
                    if (response.data) {
                        const order = response.data.find(
                            (o) => o.id === orderId,
                        );
                        if (order && order.tripId) {
                            // Found the trip ID, navigate to trip info
                            pollSubscription.unsubscribe();
                            this.router.navigate(['/main/trip', order.tripId]);
                        } else if (pollCount >= maxPolls) {
                            // Stop polling after max attempts
                            pollSubscription.unsubscribe();
                            // Refresh the orders list as fallback
                            this.loadOrders();
                        }
                    }
                },
                error: (error) => {
                    console.error('Error polling for trip ID:', error);
                    if (pollCount >= maxPolls) {
                        pollSubscription.unsubscribe();
                        this.loadOrders();
                    }
                },
            });
        });
    }

    ngOnDestroy(): void {
        if (this.dialogRef) {
            this.dialogRef.close();
        }
    }
}
